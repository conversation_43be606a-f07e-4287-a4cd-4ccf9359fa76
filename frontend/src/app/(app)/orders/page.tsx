'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Eye, Package, Truck } from 'lucide-react';

const Orders: React.FC = () => {
  const orders = [
    { id: 1234, customer: '<PERSON>', status: 'Processing', total: 129.99, items: 3 },
    { id: 1235, customer: '<PERSON>', status: 'Shipped', total: 89.50, items: 2 },
    { id: 1236, customer: '<PERSON>', status: 'Delivered', total: 199.99, items: 1 },
    { id: 1237, customer: '<PERSON>', status: 'Pending', total: 75.25, items: 4 },
    { id: 1238, customer: '<PERSON>', status: 'Processing', total: 299.99, items: 2 },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Pending':
        return <Package className="h-4 w-4" />;
      case 'Processing':
        return <Package className="h-4 w-4 text-blue-500" />;
      case 'Shipped':
        return <Truck className="h-4 w-4 text-orange-500" />;
      case 'Delivered':
        return <Package className="h-4 w-4 text-green-500" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Orders</h1>
        <p className="text-muted-foreground">Track and manage customer orders</p>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <ShoppingCart className="h-8 w-8 text-muted-foreground" />
                  <div>
                    <h3 className="font-semibold">Order #{order.id}</h3>
                    <p className="text-sm text-muted-foreground">{order.customer}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Items</p>
                    <p className="font-semibold">{order.items}</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Total</p>
                    <p className="font-semibold">${order.total}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(order.status)}
                    <span className="text-sm font-medium">{order.status}</span>
                  </div>
                  
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Orders;
