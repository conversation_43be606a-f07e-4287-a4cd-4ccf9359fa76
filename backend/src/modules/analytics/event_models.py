"""
Analytics event models for ProductVideo platform.
Tracks video interactions, conversions, and user behavior.
"""

from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from core.db.database import Base


class EventType(PyEnum):
    """Analytics event types."""
    VIDEO_VIEW = "video_view"
    VIDEO_PLAY = "video_play"
    VIDEO_PAUSE = "video_pause"
    VIDEO_COMPLETE = "video_complete"
    VIDEO_SEEK = "video_seek"
    CTA_CLICK = "cta_click"
    ADD_TO_CART = "add_to_cart"
    PURCHASE = "purchase"
    PAGE_VIEW = "page_view"
    PRODUCT_VIEW = "product_view"


class AnalyticsEvent(Base):
    """
    Analytics event tracking for video interactions and conversions.
    Supports deduplication and conversion attribution.
    """
    __tablename__ = 'analytics_events'

    id = Column(Integer, primary_key=True, index=True)
    
    # Event identification
    event_id = Column(String(255), unique=True, index=True, nullable=False)  # UUID for deduplication
    event_type = Column(String(50), nullable=False)  # EventType enum value
    
    # Context
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    session_id = Column(String(255), index=True, nullable=True)
    user_id = Column(String(255), index=True, nullable=True)  # Anonymous or authenticated
    
    # Video context
    video_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    product_id = Column(String(255), index=True, nullable=True)
    
    # Event data
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    duration = Column(Float, nullable=True)  # Video duration or time spent
    position = Column(Float, nullable=True)  # Video position or scroll position
    
    # User agent and device info
    user_agent = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    country = Column(String(2), nullable=True)  # ISO country code
    device_type = Column(String(50), nullable=True)  # mobile, desktop, tablet
    
    # Attribution
    referrer = Column(Text, nullable=True)
    utm_source = Column(String(255), nullable=True)
    utm_medium = Column(String(255), nullable=True)
    utm_campaign = Column(String(255), nullable=True)
    utm_content = Column(String(255), nullable=True)
    utm_term = Column(String(255), nullable=True)
    
    # E-commerce data (for conversion events)
    order_id = Column(String(255), index=True, nullable=True)
    order_value = Column(Float, nullable=True)
    currency = Column(String(3), nullable=True)
    
    # Custom properties
    properties = Column(JSON, nullable=True, default=dict)
    
    # Processing flags
    is_processed = Column(Boolean, default=False, nullable=False)
    is_conversion = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    tenant = relationship("Tenant")
    video_variant = relationship("MediaVariant")

    def __repr__(self):
        return f"<AnalyticsEvent(id={self.id}, type='{self.event_type}', product_id='{self.product_id}')>"


# Create indexes for performance
Index('idx_analytics_events_tenant_timestamp', AnalyticsEvent.tenant_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_product_timestamp', AnalyticsEvent.product_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_session_timestamp', AnalyticsEvent.session_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_conversion', AnalyticsEvent.is_conversion, AnalyticsEvent.timestamp)


class ConversionFunnel(Base):
    """
    Conversion funnel tracking for video-to-purchase attribution.
    """
    __tablename__ = 'conversion_funnels'

    id = Column(Integer, primary_key=True, index=True)
    
    # Identification
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    session_id = Column(String(255), index=True, nullable=False)
    user_id = Column(String(255), index=True, nullable=True)
    
    # Funnel stages
    video_view_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    video_play_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    video_complete_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    cta_click_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    add_to_cart_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    purchase_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    
    # Attribution
    first_video_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    converting_video_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    product_id = Column(String(255), index=True, nullable=True)
    
    # Conversion metrics
    time_to_conversion = Column(Integer, nullable=True)  # Seconds from first video view to purchase
    conversion_value = Column(Float, nullable=True)
    currency = Column(String(3), nullable=True)
    
    # Timestamps
    funnel_start = Column(DateTime(timezone=True), nullable=False)
    funnel_end = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    first_video_variant = relationship("MediaVariant", foreign_keys=[first_video_variant_id])
    converting_video_variant = relationship("MediaVariant", foreign_keys=[converting_video_variant_id])

    def __repr__(self):
        return f"<ConversionFunnel(id={self.id}, session_id='{self.session_id}', converted={self.purchase_event_id is not None})>"


class VideoAnalytics(Base):
    """
    Aggregated video analytics for performance tracking.
    """
    __tablename__ = 'video_analytics'

    id = Column(Integer, primary_key=True, index=True)
    
    # Identification
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    video_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=False)
    product_id = Column(String(255), index=True, nullable=False)
    
    # Time period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # View metrics
    views = Column(Integer, default=0, nullable=False)
    unique_views = Column(Integer, default=0, nullable=False)
    plays = Column(Integer, default=0, nullable=False)
    completions = Column(Integer, default=0, nullable=False)
    
    # Engagement metrics
    total_watch_time = Column(Float, default=0.0, nullable=False)  # Total seconds watched
    average_watch_time = Column(Float, default=0.0, nullable=False)
    completion_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    
    # Interaction metrics
    cta_clicks = Column(Integer, default=0, nullable=False)
    cta_click_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    
    # Conversion metrics
    add_to_carts = Column(Integer, default=0, nullable=False)
    purchases = Column(Integer, default=0, nullable=False)
    conversion_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    total_revenue = Column(Float, default=0.0, nullable=False)
    
    # Device breakdown
    mobile_views = Column(Integer, default=0, nullable=False)
    desktop_views = Column(Integer, default=0, nullable=False)
    tablet_views = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    video_variant = relationship("MediaVariant")

    def __repr__(self):
        return f"<VideoAnalytics(id={self.id}, variant_id={self.video_variant_id}, views={self.views})>"


# Create indexes for analytics queries
Index('idx_video_analytics_tenant_date', VideoAnalytics.tenant_id, VideoAnalytics.date)
Index('idx_video_analytics_product_date', VideoAnalytics.product_id, VideoAnalytics.date)
Index('idx_video_analytics_variant_date', VideoAnalytics.video_variant_id, VideoAnalytics.date)


class ABTestExperiment(Base):
    """
    A/B testing experiments for video variants.
    """
    __tablename__ = 'ab_test_experiments'

    id = Column(Integer, primary_key=True, index=True)
    
    # Experiment details
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Configuration
    product_ids = Column(JSON, nullable=False)  # List of product IDs
    control_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=False)
    test_variant_ids = Column(JSON, nullable=False)  # List of variant IDs
    
    # Traffic allocation
    traffic_allocation = Column(Float, default=1.0, nullable=False)  # 0.0 to 1.0
    control_split = Column(Float, default=0.5, nullable=False)  # 0.0 to 1.0
    
    # Status
    is_active = Column(Boolean, default=False, nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    
    # Results
    statistical_significance = Column(Float, nullable=True)  # p-value
    confidence_level = Column(Float, default=0.95, nullable=False)
    winner_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    control_variant = relationship("MediaVariant", foreign_keys=[control_variant_id])
    winner_variant = relationship("MediaVariant", foreign_keys=[winner_variant_id])

    def __repr__(self):
        return f"<ABTestExperiment(id={self.id}, name='{self.name}', active={self.is_active})>"
