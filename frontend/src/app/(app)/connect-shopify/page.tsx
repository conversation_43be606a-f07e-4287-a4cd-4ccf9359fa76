"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  ShoppingBag, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink, 
  Shield, 
  Users, 
  BarChart3,
  Video,
  Loader2
} from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import ShopifyService from "@/services/shopifyService";
import { toast } from "react-hot-toast";

export default function ConnectShopifyPage() {
  const [shopDomain, setShopDomain] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);

  // Fetch connection status
  const { data: connectionData, isLoading, refetch } = useQuery({
    queryKey: ['shopify-connection'],
    queryFn: () => ShopifyService.getConnectionStatus(),
  });

  // Connect to Shopify mutation
  const connectMutation = useMutation({
    mutationFn: (domain: string) => ShopifyService.getOAuthUrl(domain),
    onSuccess: (data) => {
      // Redirect to Shopify OAuth
      window.location.href = data.oauth_url;
    },
    onError: (error) => {
      toast.error('Failed to connect to Shopify');
      console.error('Connection error:', error);
      setIsConnecting(false);
    },
  });

  // Disconnect mutation
  const disconnectMutation = useMutation({
    mutationFn: (storeId: string) => ShopifyService.disconnectStore(storeId),
    onSuccess: () => {
      toast.success('Disconnected from Shopify');
      refetch();
    },
    onError: (error) => {
      toast.error('Failed to disconnect from Shopify');
      console.error('Disconnect error:', error);
    },
  });

  const handleConnect = () => {
    if (!shopDomain.trim()) {
      toast.error('Please enter your shop domain');
      return;
    }

    // Clean up domain input
    let domain = shopDomain.trim().toLowerCase();
    if (domain.includes('.myshopify.com')) {
      domain = domain.replace('.myshopify.com', '');
    }
    if (domain.includes('https://')) {
      domain = domain.replace('https://', '');
    }
    if (domain.includes('http://')) {
      domain = domain.replace('http://', '');
    }

    setIsConnecting(true);
    connectMutation.mutate(domain);
  };

  const handleDisconnect = (storeId: string) => {
    if (confirm('Are you sure you want to disconnect from Shopify? This will stop video generation and analytics tracking.')) {
      disconnectMutation.mutate(storeId);
    }
  };

  const isConnected = connectionData?.isConnected;
  const stores = connectionData?.stores || [];

  const requiredScopes = [
    { name: 'read_products', description: 'Read product information for video generation' },
    { name: 'write_products', description: 'Add generated videos to products' },
    { name: 'read_product_listings', description: 'Access product collections and tags' },
    { name: 'write_files', description: 'Upload video files to Shopify' },
    { name: 'read_orders', description: 'Track conversion analytics' },
  ];

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Connect to Shopify</h1>
        <p className="text-muted-foreground">
          Connect your Shopify store to start generating AI-powered product videos
        </p>
      </div>

      {isConnected ? (
        /* Connected State */
        <div className="space-y-6">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Your Shopify store is connected and ready for video generation!
            </AlertDescription>
          </Alert>

          {/* Connected Stores */}
          <div className="grid gap-4">
            {stores.map((store) => (
              <Card key={store.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <ShoppingBag className="h-6 w-6 text-green-600" />
                      <div>
                        <CardTitle className="text-lg">{store.name}</CardTitle>
                        <CardDescription>{store.domain}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Connected
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(store.id)}
                        disabled={disconnectMutation.isPending}
                      >
                        {disconnectMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          'Disconnect'
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{store.productCount || 0}</div>
                      <div className="text-sm text-muted-foreground">Products</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{store.videosGenerated || 0}</div>
                      <div className="text-sm text-muted-foreground">Videos Generated</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{store.lastSync || 'Never'}</div>
                      <div className="text-sm text-muted-foreground">Last Sync</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <Video className="h-8 w-8 mx-auto text-purple-600" />
                <CardTitle className="text-lg">Generate Videos</CardTitle>
                <CardDescription>
                  Start creating AI videos for your products
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <BarChart3 className="h-8 w-8 mx-auto text-blue-600" />
                <CardTitle className="text-lg">View Analytics</CardTitle>
                <CardDescription>
                  Track video performance and conversions
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="text-center">
                <Users className="h-8 w-8 mx-auto text-green-600" />
                <CardTitle className="text-lg">Manage Products</CardTitle>
                <CardDescription>
                  Browse and organize your product catalog
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      ) : (
        /* Not Connected State */
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Connection Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingBag className="mr-2 h-5 w-5" />
                Connect Your Store
              </CardTitle>
              <CardDescription>
                Enter your Shopify store domain to get started
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="shop-domain">Shop Domain</Label>
                <div className="flex space-x-2">
                  <Input
                    id="shop-domain"
                    placeholder="your-store-name"
                    value={shopDomain}
                    onChange={(e) => setShopDomain(e.target.value)}
                    disabled={isConnecting}
                  />
                  <span className="flex items-center text-muted-foreground">.myshopify.com</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter just the store name (e.g., "my-store" for my-store.myshopify.com)
                </p>
              </div>

              <Button 
                onClick={handleConnect}
                disabled={isConnecting || !shopDomain.trim()}
                className="w-full"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Connect to Shopify
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Required Permissions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Required Permissions
              </CardTitle>
              <CardDescription>
                ProductVideo will request the following permissions from your Shopify store
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requiredScopes.map((scope) => (
                  <div key={scope.name} className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-sm">{scope.name}</div>
                      <div className="text-xs text-muted-foreground">{scope.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Benefits */}
          <Card>
            <CardHeader>
              <CardTitle>Why Connect Your Store?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Seamless Integration</h4>
                  <p className="text-sm text-muted-foreground">
                    Automatically sync products and push generated videos directly to your store
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Advanced Analytics</h4>
                  <p className="text-sm text-muted-foreground">
                    Track video performance, conversion rates, and customer engagement
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Bulk Operations</h4>
                  <p className="text-sm text-muted-foreground">
                    Generate videos for multiple products at once and manage them efficiently
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Real-time Sync</h4>
                  <p className="text-sm text-muted-foreground">
                    Keep your product catalog and videos in sync with automatic updates
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Your store data is secure. We only access the minimum required information to provide our services 
              and never store sensitive customer or payment data.
            </AlertDescription>
          </Alert>
        </div>
      )}
    </div>
  );
}
