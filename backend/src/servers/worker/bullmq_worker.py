"""
BullMQ worker implementation for ProductVideo platform.
Replaces custom worker with Bull<PERSON><PERSON> Worker.
"""

import asyncio
import logging
import os
import signal
import sys
from typing import Dict, Any, Optional
from datetime import datetime

from bullmq import Worker, Job
import redis.asyncio as redis

from core.config import get_settings
from core.db.database import get_db
from modules.media_generation.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media_generation.image_service import ai_image_service
from modules.media_generation.video_service import ai_video_service
from modules.media_generation.voice_service import ai_voice_service
from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult
from modules.media_generation.storage_service import video_storage_service
from modules.media_generation.transcoding_service import video_transcoding_service
from modules.shopify.media_service import shopify_media_service
from modules.billing.service import billing_service
from modules.analytics.event_service import analytics_event_service

import pydantic
logger.info(f"Pydantic version: {pydantic.VERSION}")
logger = logging.getLogger(__name__)
settings = get_settings()


class ProductMediaWorker:
    """
    BullMQ worker for ProductVideo platform.
    Handles video generation, Shopify push, and analytics processing.
    """
    
    def __init__(self, worker_id: str = None):
        self.worker_id = worker_id or f"worker-{os.getpid()}"
        self.redis_connection = None
        self.workers = []
        self.shutdown_event = asyncio.Event()
        
        logger.info(f"Initialized ProductVideo worker {self.worker_id}")
    
    async def start(self):
        """Start all BullMQ workers."""
        logger.info(f"Starting ProductVideo worker {self.worker_id}")
        
        # Connect to Redis
        self.redis_connection = redis.from_url(settings.REDIS_URL)
        
        # Create workers for different queues
        self.workers = [
            # Video generation worker
            Worker(
                "media-generation",
                self._process_media_generation,
                connection=self.redis_connection,
                concurrency=2,  # Process 2 video jobs concurrently
            ),
            
            # Video push worker
            Worker(
                "media-push",
                self._process_media_push,
                connection=self.redis_connection,
                concurrency=5,  # Higher concurrency for API calls
            ),
            
            # Analytics processing worker
            Worker(
                "analytics-processing",
                self._process_analytics,
                connection=self.redis_connection,
                concurrency=10,  # High concurrency for analytics
            ),
        ]
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        # Start all workers
        for worker in self.workers:
            await worker.run()
        
        logger.info(f"Started {len(self.workers)} BullMQ workers")
        
        # Wait for shutdown
        await self.shutdown_event.wait()
        
        # Cleanup
        await self.stop()
    
    async def stop(self):
        """Stop all workers and cleanup."""
        logger.info("Stopping ProductVideo worker...")
        
        # Close all workers
        for worker in self.workers:
            await worker.close()
        
        # Close Redis connection
        if self.redis_connection:
            await self.redis_connection.close()
        
        logger.info("ProductVideo worker stopped")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _process_media_generation(self, job: Job) -> Dict[str, Any]:
        """
        Process video generation job.
        
        Args:
            job: BullMQ job instance
            
        Returns:
            Job result
        """
        job_data = job.data
        tenant_id = job_data["tenant_id"]
        db_job_id = job_data["job_id"]
        product_ids = job_data["product_ids"]
        template_id = job_data["template_id"]
        voice_id = job_data["voice_id"]
        
        media_type = job_data["media_type"]
        logger.info(f"Processing {media_type} generation job {job.id} for tenant {tenant_id}")
        
        try:
            async for db in get_db():
                # Get media job from database
                media_job = await db.get(MediaJob, db_job_id)
                if not media_job:
                    raise Exception(f"Media job {db_job_id} not found")
                
                # Update job status
                media_job.status = MediaJobStatus.PROCESSING
                await db.commit()
                
                # Update job progress
                await job.updateProgress(10)
                
                # Process each product
                total_products = len(product_ids)
                for i, product_id in enumerate(product_ids):
                    await self._generate_media_for_product(
                        db, media_job, product_id, template_id, voice_id, media_type
                    )
                    
                    # Update progress
                    progress = 10 + (80 * (i + 1) / total_products)
                    await job.updateProgress(progress)
                
                # Record billing usage
                await billing_service.record_video_generation_usage(
                    db, tenant_id, str(db_job_id), len(product_ids) * 4  # 4 variants per product
                )
                
                # Update job status to completed
                media_job.status = MediaJobStatus.COMPLETED
                media_job.completed_at = datetime.utcnow()
                await db.commit()
                
                await job.updateProgress(100)
                
                logger.info(f"{media_type} generation job {job.id} completed successfully")
                
                return {
                    "success": True,
                    "job_id": db_job_id,
                    "products_processed": len(product_ids),
                    "variants_generated": len(product_ids) * 4
                }
                
        except Exception as e:
            logger.error(f"{media_type} generation job {job.id} failed: {e}")
            
            # Update database job status
            async for db in get_db():
                media_job = await db.get(MediaJob, db_job_id)
                if media_job:
                    media_job.status = MediaJobStatus.FAILED
                    media_job.error_message = str(e)
                    await db.commit()
                break
            
            raise e
    
    async def _generate_media_for_product(
        self,
        db,
        media_job: MediaJob,
        product_id: str,
        template_id: str,
        voice_id: str,
        media_type: str
    ):
        """Generate media variants for a single product."""
        logger.info(f"Generating {media_type} for product {product_id}")
        
        request = MediaGenerationRequest(
            media_type=media_type,
            product_id=product_id,
            template_id=template_id,
            voice_id=voice_id,
            script=media_job.script or f"Amazing product {product_id}",
            aspect_ratio="16:9",
            variants_count=4
        )
        
        if media_type == "video":
            result = await ai_video_service.generate(request)
        elif media_type == "image":
            result = await ai_image_service.generate(request)
        elif media_type == "voice":
            result = await ai_voice_service.generate(request)
        else:
            raise ValueError(f"Unsupported media type: {media_type}")
        
        if result.success:
            for i, variant_data in enumerate(result.variants):
                variant = MediaVariant(
                    job_id=media_job.id,
                    product_id=product_id,
                    variant_name=f"Variant {i+1}",
                    status=MediaVariantStatus.PROCESSING,
                    provider_media_id=variant_data.get("id"),
                    generation_params=variant_data
                )
                db.add(variant)
                await db.flush()
                
                media_url = variant_data.get("video_url") or variant_data.get("image_url") or variant_data.get("voice_url")
                if media_url:
                    try:
                        if media_type == "video":
                            processing_result = await video_transcoding_service.process_video(
                                video_url=media_url,
                                output_formats=['mp4', 'hls'],
                                generate_thumbnails=True,
                                generate_subtitles=True,
                                thumbnail_count=3
                            )
                            storage_results = await self._upload_processed_video(
                                media_job.tenant_id, variant.id, processing_result
                            )
                            variant.video_url = storage_results.get('mp4_url')
                            variant.hls_url = storage_results.get('hls_url')
                            variant.thumbnail_urls = storage_results.get('thumbnail_urls', [])
                            variant.subtitle_url = storage_results.get('subtitle_url')
                        elif media_type == "image":
                            # For images, directly upload to storage
                            image_content = await ai_image_service.download_media(media_url)
                            image_key = f"tenants/{media_job.tenant_id}/images/{variant.id}/image.jpg"
                            image_url = await video_storage_service.upload_file(image_content, image_key) # Assuming upload_file can take content directly
                            variant.image_url = image_url
                        elif media_type == "voice":
                            # For voice, directly upload to storage
                            voice_content = await ai_voice_service.download_media(media_url)
                            voice_key = f"tenants/{media_job.tenant_id}/voices/{variant.id}/voice.mp3"
                            voice_url = await video_storage_service.upload_file(voice_content, voice_key) # Assuming upload_file can take content directly
                            variant.voice_url = voice_url
                        
                        variant.status = MediaVariantStatus.READY
                        logger.info(f"Processed variant {variant.id} for product {product_id}")
                        
                    except Exception as e:
                        logger.error(f"Failed to process variant {variant.id}: {e}")
                        variant.status = MediaVariantStatus.FAILED
                        variant.error_message = str(e)
                else:
                    variant.status = MediaVariantStatus.FAILED
                    variant.error_message = "No media URL provided"
            
            await db.commit()
            logger.info(f"Generated and processed {len(result.variants)} variants for product {product_id}")
        else:
            raise Exception(f"{media_type} generation failed: {result.error_message}")
    
    async def _upload_processed_media(
        self, 
        tenant_id: int, 
        variant_id: int, 
        processing_result: dict,
        media_type: str
    ) -> dict:
        """Upload processed media files to storage."""
        storage_results = {}
        
        try:
            if media_type == "video":
                # Upload MP4 video
                if 'mp4' in processing_result['outputs']:
                    mp4_path = processing_result['outputs']['mp4']
                    mp4_key = f"tenants/{tenant_id}/videos/{variant_id}/video.mp4"
                    mp4_url = await video_storage_service.upload_file(mp4_path, mp4_key)
                    storage_results['mp4_url'] = mp4_url
                
                # Upload HLS playlist and segments
                if 'hls' in processing_result['outputs']:
                    hls_path = processing_result['outputs']['hls']
                    hls_dir = os.path.dirname(hls_path)
                    
                    # Upload all HLS files
                    hls_urls = []
                    for file_name in os.listdir(hls_dir):
                        file_path = os.path.join(hls_dir, file_name)
                        hls_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/{file_name}"
                        hls_url = await video_storage_service.upload_file(file_path, hls_key)
                        hls_urls.append(hls_url)
                    
                    # The main playlist URL
                    playlist_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/playlist.m3u8"
                    storage_results['hls_url'] = f"{video_storage_service.base_url}/{playlist_key}"
                
                # Upload thumbnails
                thumbnail_urls = []
                for i, thumb_path in enumerate(processing_result['thumbnails']):
                    thumb_key = f"tenants/{tenant_id}/videos/{variant_id}/thumbnails/thumb_{i+1}.jpg"
                    thumb_url = await video_storage_service.upload_file(thumb_path, thumb_key)
                    thumbnail_urls.append(thumb_url)
                storage_results['thumbnail_urls'] = thumbnail_urls
                
                # Upload subtitles
                if processing_result['subtitles']:
                    subtitle_path = processing_result['subtitles']
                    subtitle_key = f"tenants/{tenant_id}/videos/{variant_id}/subtitles.srt"
                    subtitle_url = await video_storage_service.upload_file(subtitle_path, subtitle_key)
                    storage_results['subtitle_url'] = subtitle_url
            elif media_type == "image":
                # Image processing results would be different, handle accordingly
                pass
            elif media_type == "voice":
                # Voice processing results would be different, handle accordingly
                pass
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Failed to upload processed media files: {e}")
            raise
    
    async def _process_media_push(self, job: Job) -> Dict[str, Any]:
        """
        Process media push to Shopify job.
        
        Args:
            job: BullMQ job instance
            
        Returns:
            Job result
        """
        job_data = job.data
        tenant_id = job_data["tenant_id"]
        variant_id = job_data["variant_id"]
        product_id = job_data["product_id"]
        shop_domain = job_data["shop_domain"]
        
        logger.info(f"Processing media push job {job.id} for variant {variant_id}")
        
        try:
            async for db in get_db():
                # Get media variant
                variant = await db.get(MediaVariant, variant_id)
                if not variant:
                    raise Exception(f"Media variant {variant_id} not found")
                
                # Determine media URL based on type
                media_url = variant.video_url or variant.image_url or variant.voice_url
                if not media_url:
                    raise Exception(f"Media variant {variant_id} has no media URL")
                
                # Update progress
                await job.updateProgress(25)
                
                # Push to Shopify
                push_result = await shopify_media_service.push_media_to_product(
                    shop_domain=shop_domain,
                    product_id=product_id,
                    media_url=media_url,
                    alt_text=f"Product media - {variant.variant_name}"
                )
                
                await job.updateProgress(75)
                
                if push_result.get("success"):
                    # Update variant status
                    variant.shopify_media_id = push_result.get("media_id")
                    variant.pushed_to_shopify_at = datetime.utcnow()
                    await db.commit()
                    
                    await job.updateProgress(100)
                    
                    logger.info(f"Successfully pushed variant {variant_id} to Shopify")
                    
                    return {
                        "success": True,
                        "variant_id": variant_id,
                        "shopify_media_id": push_result.get("media_id")
                    }
                else:
                    raise Exception(push_result.get("error", "Unknown error"))
                
        except Exception as e:
            logger.error(f"Media push job {job.id} failed: {e}")
            raise e
    
    async def _process_analytics(self, job: Job) -> Dict[str, Any]:
        """
        Process analytics event.
        
        Args:
            job: BullMQ job instance
            
        Returns:
            Job result
        """
        job_data = job.data
        tenant_id = job_data["tenant_id"]
        event_data = job_data["event_data"]
        
        logger.info(f"Processing analytics job {job.id} for tenant {tenant_id}")
        
        try:
            async for db in get_db():
                # Process the analytics event
                from modules.analytics.event_schemas import EventIngestionRequest
                
                event_request = EventIngestionRequest(**event_data)
                
                response = await analytics_event_service.ingest_event(
                    db, tenant_id, event_request
                )
                
                logger.info(f"Analytics job {job.id} completed: {response.status}")
                
                return {
                    "success": True,
                    "event_id": response.event_id,
                    "status": response.status
                }
                
        except Exception as e:
            logger.error(f"Analytics job {job.id} failed: {e}")
            raise e


async def main():
    """Main entry point for BullMQ worker."""
    worker_id = os.getenv('WORKER_ID', f"worker-{os.getpid()}")
    
    worker = ProductMediaWorker(worker_id)
    
    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Worker error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
