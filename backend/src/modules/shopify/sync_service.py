"""
Shopify sync service - essential entities for ProductVideo platform.
Focuses on products, orders, and customers with proper rate limiting and error handling.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.shopify.models import (
    ShopifyProduct, ShopifyProductVariant, ShopifyCustomer, 
    ShopifyOrder, ShopifyOrderLineItem, ShopifyProductMedia
)

logger = logging.getLogger(__name__)
settings = get_settings()


class ShopifySyncService:
    """
    Shopify sync service for ProductVideo platform.
    Syncs essential entities: products, orders, customers.
    """
    
    def __init__(self, store_domain: str, access_token: str, shopify_integration_id: int = 1):
        self.store_domain = store_domain
        self.access_token = access_token
        self.shopify_integration_id = shopify_integration_id
        self.api_version = "2025-07"
        self.base_url = f"https://{store_domain}/admin/api/{self.api_version}"
        
        # Rate limiting
        self.rate_limit_remaining = 40
        self.rate_limit_reset_time = None
        
        # HTTP client
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "X-Shopify-Access-Token": access_token,
                "Content-Type": "application/json"
            }
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make rate-limited request to Shopify API."""
        await self._check_rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        response = await self.client.request(method, url, **kwargs)
        
        # Update rate limit info
        self.rate_limit_remaining = int(response.headers.get("X-Shopify-Shop-Api-Call-Limit", "40/40").split("/")[0])
        
        if response.status_code == 429:
            # Rate limited - wait and retry
            retry_after = int(response.headers.get("Retry-After", 2))
            logger.warning(f"Rate limited, waiting {retry_after} seconds")
            await asyncio.sleep(retry_after)
            return await self._make_request(method, endpoint, **kwargs)
        
        response.raise_for_status()
        return response.json()
    
    async def _check_rate_limit(self):
        """Check and respect rate limits."""
        if self.rate_limit_remaining <= 2:
            # Wait for rate limit reset
            wait_time = 1.0  # Conservative wait
            logger.info(f"Rate limit low ({self.rate_limit_remaining}), waiting {wait_time}s")
            await asyncio.sleep(wait_time)
            self.rate_limit_remaining = 40  # Reset
    
    async def sync_products(
        self, 
        db: AsyncSession, 
        limit: int = 250,
        since_id: Optional[str] = None
    ) -> Dict[str, int]:
        """
        Sync products from Shopify.
        
        Returns:
            Dict with sync statistics
        """
        logger.info("Starting product sync")
        
        params = {"limit": limit}
        if since_id:
            params["since_id"] = since_id
        
        try:
            response = await self._make_request("GET", "products.json", params=params)
            products = response.get("products", [])
            
            stats = {"added": 0, "updated": 0, "unchanged": 0}
            
            for product_data in products:
                await self._sync_single_product(db, product_data, stats)
            
            await db.commit()
            logger.info(f"Product sync completed: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Product sync failed: {e}")
            await db.rollback()
            raise
    
    async def _sync_single_product(
        self, 
        db: AsyncSession, 
        product_data: Dict[str, Any], 
        stats: Dict[str, int]
    ):
        """Sync a single product."""
        product_id = str(product_data["id"])
        
        # Check if product exists
        existing_product = await db.get(ShopifyProduct, product_id)
        
        if existing_product:
            # Update existing product
            updated = False
            if existing_product.title != product_data["title"]:
                existing_product.title = product_data["title"]
                updated = True
            if existing_product.description != product_data.get("body_html"):
                existing_product.description = product_data.get("body_html")
                updated = True
            
            if updated:
                existing_product.shopify_updated_at = datetime.fromisoformat(
                    product_data["updated_at"].replace("Z", "+00:00")
                )
                stats["updated"] += 1
            else:
                stats["unchanged"] += 1
        else:
            # Create new product
            new_product = ShopifyProduct(
                external_id=product_id,
                shopify_integration_id=self.shopify_integration_id,
                title=product_data["title"],
                description=product_data.get("body_html"),
                handle=product_data.get("handle"),
                product_type=product_data.get("product_type"),
                vendor=product_data.get("vendor"),
                status=product_data.get("status"),
                tags=product_data.get("tags"),
                featured_image_url=product_data.get("image", {}).get("src") if product_data.get("image") else None,
                images=[img["src"] for img in product_data.get("images", [])],
                seo_title=product_data.get("seo_title"),
                seo_description=product_data.get("seo_description"),
                shopify_created_at=datetime.fromisoformat(
                    product_data["created_at"].replace("Z", "+00:00")
                ),
                shopify_updated_at=datetime.fromisoformat(
                    product_data["updated_at"].replace("Z", "+00:00")
                ),
                published_at=datetime.fromisoformat(
                    product_data["published_at"].replace("Z", "+00:00")
                ) if product_data.get("published_at") else None
            )
            
            db.add(new_product)
            stats["added"] += 1
        
        # Sync variants
        for variant_data in product_data.get("variants", []):
            await self._sync_product_variant(db, variant_data, product_id)
    
    async def _sync_product_variant(
        self, 
        db: AsyncSession, 
        variant_data: Dict[str, Any], 
        product_id: str
    ):
        """Sync a product variant."""
        variant_id = str(variant_data["id"])
        
        existing_variant = await db.get(ShopifyProductVariant, variant_id)
        
        if not existing_variant:
            new_variant = ShopifyProductVariant(
                external_id=variant_id,
                shopify_integration_id=self.shopify_integration_id,
                product_id=product_id,
                title=variant_data.get("title"),
                sku=variant_data.get("sku"),
                barcode=variant_data.get("barcode"),
                price=float(variant_data["price"]) if variant_data.get("price") else None,
                compare_at_price=float(variant_data["compare_at_price"]) if variant_data.get("compare_at_price") else None,
                inventory_quantity=variant_data.get("inventory_quantity"),
                inventory_management=variant_data.get("inventory_management"),
                inventory_policy=variant_data.get("inventory_policy"),
                weight=float(variant_data["weight"]) if variant_data.get("weight") else None,
                weight_unit=variant_data.get("weight_unit"),
                option1=variant_data.get("option1"),
                option2=variant_data.get("option2"),
                option3=variant_data.get("option3"),
                available=variant_data.get("available", True),
                shopify_created_at=datetime.fromisoformat(
                    variant_data["created_at"].replace("Z", "+00:00")
                ),
                shopify_updated_at=datetime.fromisoformat(
                    variant_data["updated_at"].replace("Z", "+00:00")
                )
            )
            
            db.add(new_variant)
    
    async def sync_orders(
        self, 
        db: AsyncSession, 
        limit: int = 250,
        since_id: Optional[str] = None
    ) -> Dict[str, int]:
        """Sync orders from Shopify."""
        logger.info("Starting order sync")
        
        params = {"limit": limit, "status": "any"}
        if since_id:
            params["since_id"] = since_id
        
        try:
            response = await self._make_request("GET", "orders.json", params=params)
            orders = response.get("orders", [])
            
            stats = {"added": 0, "updated": 0, "unchanged": 0}
            
            for order_data in orders:
                await self._sync_single_order(db, order_data, stats)
            
            await db.commit()
            logger.info(f"Order sync completed: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Order sync failed: {e}")
            await db.rollback()
            raise
    
    async def _sync_single_order(
        self, 
        db: AsyncSession, 
        order_data: Dict[str, Any], 
        stats: Dict[str, int]
    ):
        """Sync a single order."""
        order_id = str(order_data["id"])
        
        existing_order = await db.get(ShopifyOrder, order_id)
        
        if not existing_order:
            new_order = ShopifyOrder(
                external_id=order_id,
                shopify_integration_id=self.shopify_integration_id,
                order_number=str(order_data.get("order_number")),
                name=order_data.get("name"),
                email=order_data.get("email"),
                customer_id=str(order_data["customer"]["id"]) if order_data.get("customer") else None,
                total_price=float(order_data["total_price"]) if order_data.get("total_price") else None,
                subtotal_price=float(order_data["subtotal_price"]) if order_data.get("subtotal_price") else None,
                total_tax=float(order_data["total_tax"]) if order_data.get("total_tax") else None,
                total_discounts=float(order_data["total_discounts"]) if order_data.get("total_discounts") else None,
                currency=order_data.get("currency"),
                financial_status=order_data.get("financial_status"),
                fulfillment_status=order_data.get("fulfillment_status"),
                source_name=order_data.get("source_name"),
                referring_site=order_data.get("referring_site"),
                landing_site=order_data.get("landing_site"),
                shopify_created_at=datetime.fromisoformat(
                    order_data["created_at"].replace("Z", "+00:00")
                ),
                shopify_updated_at=datetime.fromisoformat(
                    order_data["updated_at"].replace("Z", "+00:00")
                ),
                processed_at=datetime.fromisoformat(
                    order_data["processed_at"].replace("Z", "+00:00")
                ) if order_data.get("processed_at") else None
            )
            
            db.add(new_order)
            stats["added"] += 1
            
            # Sync line items
            for line_item in order_data.get("line_items", []):
                await self._sync_order_line_item(db, line_item, order_id)
    
    async def _sync_order_line_item(
        self, 
        db: AsyncSession, 
        line_item_data: Dict[str, Any], 
        order_id: str
    ):
        """Sync an order line item."""
        line_item_id = str(line_item_data["id"])
        
        existing_item = await db.get(ShopifyOrderLineItem, line_item_id)
        
        if not existing_item:
            new_item = ShopifyOrderLineItem(
                external_id=line_item_id,
                shopify_integration_id=self.shopify_integration_id,
                order_id=order_id,
                product_id=str(line_item_data["product_id"]) if line_item_data.get("product_id") else None,
                variant_id=str(line_item_data["variant_id"]) if line_item_data.get("variant_id") else None,
                title=line_item_data.get("title"),
                name=line_item_data.get("name"),
                sku=line_item_data.get("sku"),
                quantity=line_item_data.get("quantity", 0),
                price=float(line_item_data["price"]) if line_item_data.get("price") else None,
                total_discount=float(line_item_data["total_discount"]) if line_item_data.get("total_discount") else None,
                fulfillment_status=line_item_data.get("fulfillment_status"),
                fulfillable_quantity=line_item_data.get("fulfillable_quantity")
            )
            
            db.add(new_item)
    
    async def get_product_count(self) -> int:
        """Get total product count."""
        try:
            response = await self._make_request("GET", "products/count.json")
            return response.get("count", 0)
        except Exception as e:
            logger.error(f"Failed to get product count: {e}")
            return 0
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test Shopify API connection."""
        try:
            response = await self._make_request("GET", "shop.json")
            shop = response.get("shop", {})
            
            return {
                "success": True,
                "shop_name": shop.get("name"),
                "shop_domain": shop.get("domain"),
                "shop_id": shop.get("id"),
                "plan": shop.get("plan_name"),
                "currency": shop.get("currency")
            }
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
