import { api } from "../services/api";

export interface OrderLineItem {
  id: string;
  title: string;
  quantity: number;
  price: number;
  product_id?: string;
  variant_id?: string;
  sku?: string;
  vendor?: string;
}

export interface Order {
  id: number;
  external_id: string;
  order_number: string;
  customer_email: string;
  customer_id?: string;
  total_price: number;
  subtotal_price?: number;
  total_tax?: number;
  total_discounts?: number;
  currency: string;
  status: string;
  financial_status?: string;
  fulfillment_status?: string;
  order_date: string;
  line_items: OrderLineItem[];
  store_id: number;
}

export interface PaginatedOrders {
  total_count: number;
  orders: Order[];
}

export const fetchProductOrders = async (productId: number) => {
  const response = await api.get(`/api/products/${productId}/orders`);
  return response.data;
};

export const fetchOrders = async (
  storeId?: number,
  skip: number = 0,
  limit: number = 10,
  search?: string,
  sortBy?: string
): Promise<PaginatedOrders> => {
  const params = {
    store_id: storeId,
    skip,
    limit,
    search,
    sort_by: sortBy,
  };
  const response = await api.get("/api/orders/", { params });
  return response.data;
};

export const fetchOrderById = async (orderId: number): Promise<Order> => {
  const response = await api.get(`/api/orders/${orderId}`);
  return response.data;
};
