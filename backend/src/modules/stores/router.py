"""
API Router for Store Management
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.stores.models import Store
from modules.stores.schemas import (
    StoreCreate, 
    StoreResponse, 
    StoreUpdate, 
    StoreConnectionTest
)
from modules.stores.service import store_service
from modules.auth.models import User

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[StoreResponse])
async def get_stores(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for the current user."""
    stores = await store_service.get_by_owner(db, current_user.id)
    return stores


@router.post("/", response_model=StoreResponse)
async def create_store(
    store: StoreCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new store."""
    db_store = await store_service.create_store(db, store, current_user.id)
    return db_store


@router.get("/{store_id}", response_model=StoreResponse)
async def get_store(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")
    
    return store


@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: int,
    store_update: StoreUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this store")
    
    updated_store = await store_service.update(db, db_obj=store, obj_in=store_update)
    return updated_store


@router.delete("/{store_id}")
async def delete_store(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this store")
    
    await store_service.remove(db, id=store_id)
    return {"message": "Store deleted successfully"}


@router.post("/{store_id}/test-connection", response_model=StoreConnectionTest)
async def test_store_connection(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Test connection to a store's platform."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to test this store")
    
    connection_result = await store_service.test_connection(store)
    return connection_result


@router.get("/platform/{platform}", response_model=List[StoreResponse])
async def get_stores_by_platform(
    platform: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for a specific platform."""
    stores = await store_service.get_by_platform(db, platform)
    # Filter by ownership
    user_stores = [store for store in stores if store.owner_id == current_user.id]
    return user_stores
