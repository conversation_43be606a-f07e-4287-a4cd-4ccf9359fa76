"""
Webhooks models for ProductVideo platform.
Contains models for webhook event tracking.
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.sql import func

from core.db.database import Base


class WebhookEvent(Base):
    """Database model for tracking webhook events."""
    __tablename__ = 'webhook_events'

    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(String(255), unique=True, index=True, nullable=False)  # Shopify webhook ID
    topic = Column(String(100), nullable=False)
    shop_domain = Column(String(255), nullable=False)
    payload = Column(JSON, nullable=False)
    status = Column(String(20), default="pending")  # pending, processing, completed, failed, retrying
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    last_error = Column(Text, nullable=True)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<WebhookEvent(id={self.id}, topic='{self.topic}', status='{self.status}')>"