import os
from typing import Async<PERSON>enerator, Generator
from unittest.mock import patch

import pytest
import pytest_asyncio
from httpx import ASGITransport, AsyncClient
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

# Set testing environment
os.environ["TESTING"] = "True"

from passlib.context import CryptContext

from core.config import get_settings
from core.db.database import Base, get_db
from core.utils.logging import setup_logging
from modules.auth.service import auth_service
from modules.auth.models import User
from src.main import app

setup_logging()

settings = get_settings()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@pytest_asyncio.fixture(scope="function")
async def db_engine() -> AsyncGenerator[AsyncEngine, None]:
    """
    Pytest fixture to create a test database engine for each test.
    Uses SQLite in-memory database for testing.
    """
    # Use SQLite in-memory database for testing
    test_db_url = "sqlite+aiosqlite:///:memory:"
    engine = create_async_engine(test_db_url, echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def db_session(db_engine: AsyncEngine) -> AsyncGenerator[AsyncSession, None]:
    """
    Pytest fixture to provide an async database session for each test function.
    """
    Session = async_sessionmaker(bind=db_engine, expire_on_commit=False)
    async with Session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """
    Pytest fixture to create a basic, unauthenticated async test client.
    """

    def override_get_db() -> Generator[AsyncSession, None, None]:
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as c:
        yield c

    del app.dependency_overrides[get_db]


@pytest_asyncio.fixture(scope="function")
async def test_user(db_session: AsyncSession) -> User:
    """
    Pytest fixture to create a test user.
    """
    test_user = User(
        email="<EMAIL>",
        hashed_password=pwd_context.hash("testpassword"),
        full_name="Test User",
        is_active=True,
    )

    db_session.add(test_user)
    await db_session.commit()
    await db_session.refresh(test_user)

    return test_user


@pytest_asyncio.fixture(scope="function")
async def authenticated_client(
    db_session: AsyncSession, client: AsyncClient, test_user: User
) -> AsyncClient:
    """
    Pytest fixture to create an authenticated async test client.
    """
    token = auth_service.create_access_token(data={"sub": test_user.email})
    client.headers.update({"Authorization": f"Bearer {token}"})

    return client


@pytest.fixture
def mock_shopify_oauth():
    """Mock Shopify OAuth service for testing."""
    with patch("modules.shopify.oauth_service.shopify_oauth_service") as mock:
        mock.generate_install_url.return_value = "https://test-shop.myshopify.com/admin/oauth/authorize?..."
        mock.exchange_code_for_token.return_value = {
            "access_token": "test_token",
            "scope": "read_products,write_products"
        }
        mock.get_shop_info.return_value = {
            "id": 12345,
            "name": "Test Shop",
            "domain": "test-shop.myshopify.com"
        }
        yield mock
