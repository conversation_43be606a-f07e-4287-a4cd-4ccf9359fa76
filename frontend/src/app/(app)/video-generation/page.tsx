"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Play, Pause, Download, Share2, MoreHorizontal, Sparkles } from "lucide-react";
import { ProductSelectGrid } from "@/components/video-generation/ProductSelectGrid";
import { GenerateModal } from "@/components/video-generation/GenerateModal";
import { VariantCards } from "@/components/video-generation/VariantCards";
import { PushModal } from "@/components/video-generation/PushModal";
import { Gallery } from "@/components/video-generation/Gallery";
import { AnalyticsDashboard } from "@/components/video-generation/AnalyticsDashboard";

export default function VideoGenerationPage() {
  const [activeTab, setActiveTab] = useState("generate");
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showPushModal, setShowPushModal] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedVariants, setSelectedVariants] = useState<string[]>([]);

  // Mock data for demonstration
  const recentJobs = [
    {
      id: "job-1",
      status: "completed",
      productCount: 3,
      variantCount: 12,
      createdAt: "2024-01-15T10:30:00Z",
      completedAt: "2024-01-15T10:35:00Z"
    },
    {
      id: "job-2", 
      status: "processing",
      productCount: 5,
      variantCount: 20,
      createdAt: "2024-01-15T11:00:00Z",
      progress: 65
    }
  ];

  const usageStats = {
    videosGenerated: 156,
    monthlyLimit: 500,
    storageUsed: 2.4, // GB
    storageLimit: 10 // GB
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Video Generation</h1>
          <p className="text-muted-foreground">
            Create AI-powered product videos for your Shopify store
          </p>
        </div>
        <Button 
          onClick={() => setShowGenerateModal(true)}
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
        >
          <Sparkles className="mr-2 h-4 w-4" />
          Generate Videos
        </Button>
      </div>

      {/* Usage Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Videos Generated</CardTitle>
            <Badge variant="secondary">{usageStats.videosGenerated}/{usageStats.monthlyLimit}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{usageStats.videosGenerated}</div>
            <Progress 
              value={(usageStats.videosGenerated / usageStats.monthlyLimit) * 100} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <Badge variant="secondary">{usageStats.storageUsed}GB/{usageStats.storageLimit}GB</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{usageStats.storageUsed} GB</div>
            <Progress 
              value={(usageStats.storageUsed / usageStats.storageLimit) * 100} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Badge variant="outline">
              {recentJobs.filter(job => job.status === "processing").length}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recentJobs.filter(job => job.status === "processing").length}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Jobs currently processing
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate">Generate</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
          <TabsTrigger value="gallery">Gallery</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Select Products</CardTitle>
              <CardDescription>
                Choose products from your Shopify store to generate videos for
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProductSelectGrid 
                selectedProducts={selectedProducts}
                onSelectionChange={setSelectedProducts}
              />
            </CardContent>
          </Card>

          {selectedProducts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Products</CardTitle>
                <CardDescription>
                  {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-2">
                    {selectedProducts.slice(0, 3).map((productId) => (
                      <Badge key={productId} variant="secondary">
                        Product {productId}
                      </Badge>
                    ))}
                    {selectedProducts.length > 3 && (
                      <Badge variant="outline">
                        +{selectedProducts.length - 3} more
                      </Badge>
                    )}
                  </div>
                  <Button 
                    onClick={() => setShowGenerateModal(true)}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  >
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate Videos
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="variants" className="space-y-4">
          <VariantCards 
            selectedVariants={selectedVariants}
            onSelectionChange={setSelectedVariants}
            onPushSelected={() => setShowPushModal(true)}
          />
        </TabsContent>

        <TabsContent value="gallery" className="space-y-4">
          <Gallery />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <AnalyticsDashboard />
        </TabsContent>
      </Tabs>

      {/* Recent Jobs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Jobs</CardTitle>
          <CardDescription>
            Your latest video generation jobs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentJobs.map((job) => (
              <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-3 rounded-full ${
                    job.status === "completed" ? "bg-green-500" : 
                    job.status === "processing" ? "bg-blue-500" : "bg-gray-500"
                  }`} />
                  <div>
                    <p className="font-medium">Job {job.id}</p>
                    <p className="text-sm text-muted-foreground">
                      {job.productCount} products • {job.variantCount} variants
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  {job.status === "processing" && job.progress && (
                    <div className="flex items-center space-x-2">
                      <Progress value={job.progress} className="w-20" />
                      <span className="text-sm text-muted-foreground">{job.progress}%</span>
                    </div>
                  )}
                  <Badge variant={job.status === "completed" ? "default" : "secondary"}>
                    {job.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <GenerateModal 
        open={showGenerateModal}
        onOpenChange={setShowGenerateModal}
        selectedProducts={selectedProducts}
      />

      <PushModal 
        open={showPushModal}
        onOpenChange={setShowPushModal}
        selectedVariants={selectedVariants}
      />
    </div>
  );
}
