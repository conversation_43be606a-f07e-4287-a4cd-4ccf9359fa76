"""
Shopify API Router for OAuth and integration management
"""

import logging
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.shopify.oauth_service import shopify_oauth_service
from modules.auth.models import User

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/install")
async def get_install_url(
    shop: str = Query(..., description="Shop domain (e.g., 'myshop' or 'myshop.myshopify.com')"),
    current_user: User = Depends(get_current_user),
):
    """
    Generate Shopify app installation URL.
    
    Args:
        shop: Shop domain
        
    Returns:
        Installation URL and state for OAuth flow
    """
    try:
        install_url = shopify_oauth_service.generate_install_url(shop)
        return {
            "install_url": install_url,
            "message": "Visit the install_url to authorize the app"
        }
    except Exception as e:
        logger.error(f"Error generating install URL: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/oauth/callback")
async def oauth_callback(
    code: str = Query(..., description="Authorization code from Shopify"),
    shop: str = Query(..., description="Shop domain"),
    state: Optional[str] = Query(None, description="State parameter for CSRF protection"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Handle Shopify OAuth callback.
    
    Args:
        code: Authorization code from Shopify
        shop: Shop domain
        state: State parameter for CSRF protection
        
    Returns:
        Success message and store information
    """
    try:
        # Exchange code for access token
        token_data = await shopify_oauth_service.exchange_code_for_token(shop, code, state)
        
        # Get shop information
        shop_info = await shopify_oauth_service.get_shop_info(shop, token_data['access_token'])
        
        # Save store credentials
        store = await shopify_oauth_service.save_store_credentials(
            db, shop_info, token_data['access_token'], current_user.id
        )
        
        return {
            "message": "Shopify store connected successfully",
            "store": {
                "id": store.id,
                "name": store.name,
                "shop_domain": store.shop_domain,
                "shop_name": store.shop_name
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in OAuth callback: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete OAuth flow")


@router.post("/webhook/verify")
async def verify_webhook(request: Request):
    """
    Verify Shopify webhook signature.
    
    Returns:
        Verification result
    """
    try:
        # Get raw body and signature
        body = await request.body()
        signature = request.headers.get("X-Shopify-Hmac-Sha256", "")
        
        is_valid = shopify_oauth_service.verify_webhook_signature(body, signature)
        
        return {
            "valid": is_valid,
            "message": "Webhook signature verified" if is_valid else "Invalid webhook signature"
        }
        
    except Exception as e:
        logger.error(f"Error verifying webhook: {e}")
        raise HTTPException(status_code=400, detail="Failed to verify webhook")


@router.get("/scopes")
async def get_required_scopes():
    """
    Get the required Shopify scopes for the application.
    
    Returns:
        List of required scopes
    """
    return {
        "scopes": shopify_oauth_service.scopes,
        "description": "Required scopes for ProductVideo platform"
    }
