"""
Shopify models - essential entities for ProductVideo platform.
Focuses on products, orders, and customers as required.
"""

from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, BigInteger, JSON
from sqlalchemy.sql import func

from core.db.database import Base


class ShopifyIntegration(Base):
    """Core integration tracking table."""
    __tablename__ = 'shopify_integrations'

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey('stores.id'), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<ShopifyIntegration(id={self.id}, store_id={self.store_id})>"


class ShopifyProduct(Base):
    """Shopify product model - core entity for video generation."""
    __tablename__ = 'shopify_products'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Core product fields
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    handle = Column(String, nullable=True)
    product_type = Column(String, nullable=True)
    vendor = Column(String, nullable=True)
    status = Column(String, nullable=True)  # active, archived, draft
    tags = Column(String, nullable=True)
    
    # Media and images
    featured_image_url = Column(String, nullable=True)
    images = Column(JSON, nullable=True)  # Array of image URLs
    
    # SEO
    seo_title = Column(String, nullable=True)
    seo_description = Column(Text, nullable=True)
    
    # Shopify timestamps
    shopify_created_at = Column(DateTime(timezone=True), nullable=True)
    shopify_updated_at = Column(DateTime(timezone=True), nullable=True)
    published_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<ShopifyProduct(id={self.id}, external_id='{self.external_id}', title='{self.title}')>"


class ShopifyProductVariant(Base):
    """Shopify product variant model."""
    __tablename__ = 'shopify_product_variants'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    product_id = Column(String, ForeignKey('shopify_products.external_id'), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Variant details
    title = Column(String, nullable=True)
    sku = Column(String, nullable=True)
    barcode = Column(String, nullable=True)
    price = Column(Float, nullable=True)
    compare_at_price = Column(Float, nullable=True)
    
    # Inventory
    inventory_quantity = Column(Integer, nullable=True)
    inventory_management = Column(String, nullable=True)
    inventory_policy = Column(String, nullable=True)
    
    # Physical properties
    weight = Column(Float, nullable=True)
    weight_unit = Column(String, nullable=True)
    
    # Options
    option1 = Column(String, nullable=True)
    option2 = Column(String, nullable=True)
    option3 = Column(String, nullable=True)
    
    # Image
    image_url = Column(String, nullable=True)
    
    # Status
    available = Column(Boolean, default=True)
    
    # Shopify timestamps
    shopify_created_at = Column(DateTime(timezone=True), nullable=True)
    shopify_updated_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<ShopifyProductVariant(id={self.id}, external_id='{self.external_id}', title='{self.title}')>"


class ShopifyCustomer(Base):
    """Shopify customer model - needed for analytics and conversion tracking."""
    __tablename__ = 'shopify_customers'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Customer details
    email = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    
    # Status
    state = Column(String, nullable=True)  # enabled, disabled, invited, declined
    accepts_marketing = Column(Boolean, default=False)
    accepts_marketing_updated_at = Column(DateTime(timezone=True), nullable=True)
    
    # Statistics
    orders_count = Column(Integer, default=0)
    total_spent = Column(Float, default=0.0)
    
    # Shopify timestamps
    shopify_created_at = Column(DateTime(timezone=True), nullable=True)
    shopify_updated_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<ShopifyCustomer(id={self.id}, external_id='{self.external_id}', email='{self.email}')>"


class ShopifyOrder(Base):
    """Shopify order model - needed for conversion tracking and analytics."""
    __tablename__ = 'shopify_orders'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Order details
    order_number = Column(String, nullable=True)
    name = Column(String, nullable=True)  # Order name like #1001
    email = Column(String, nullable=True)

    # Customer relationship
    customer_id = Column(String, ForeignKey('shopify_customers.external_id'), nullable=True)

    # Financial
    total_price = Column(Float, nullable=True)
    subtotal_price = Column(Float, nullable=True)
    total_tax = Column(Float, nullable=True)
    total_discounts = Column(Float, nullable=True)
    currency = Column(String, nullable=True)

    # Status
    financial_status = Column(String, nullable=True)  # pending, authorized, paid, etc.
    fulfillment_status = Column(String, nullable=True)  # fulfilled, partial, unfulfilled
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    cancel_reason = Column(String, nullable=True)

    # UTM and source tracking (important for video attribution)
    source_name = Column(String, nullable=True)
    referring_site = Column(String, nullable=True)
    landing_site = Column(String, nullable=True)

    # Shopify timestamps
    shopify_created_at = Column(DateTime(timezone=True), nullable=True)
    shopify_updated_at = Column(DateTime(timezone=True), nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<ShopifyOrder(id={self.id}, external_id='{self.external_id}', name='{self.name}')>"


class ShopifyOrderLineItem(Base):
    """Shopify order line item - tracks which products were purchased."""
    __tablename__ = 'shopify_order_line_items'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    order_id = Column(String, ForeignKey('shopify_orders.external_id'), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Product relationship
    product_id = Column(String, ForeignKey('shopify_products.external_id'), nullable=True)
    variant_id = Column(String, ForeignKey('shopify_product_variants.external_id'), nullable=True)

    # Line item details
    title = Column(String, nullable=True)
    name = Column(String, nullable=True)
    sku = Column(String, nullable=True)
    quantity = Column(Integer, nullable=False)
    price = Column(Float, nullable=True)
    total_discount = Column(Float, nullable=True)

    # Fulfillment
    fulfillment_status = Column(String, nullable=True)
    fulfillable_quantity = Column(Integer, nullable=True)

    def __repr__(self):
        return f"<ShopifyOrderLineItem(id={self.id}, external_id='{self.external_id}', title='{self.title}')>"


class ShopifyProductMedia(Base):
    """Shopify product media - tracks videos and images attached to products."""
    __tablename__ = 'shopify_product_media'

    id = Column(Integer, primary_key=True, index=True)
    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)
    external_id = Column(String, unique=True, index=True, nullable=False)
    product_id = Column(String, ForeignKey('shopify_products.external_id'), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Media details
    media_type = Column(String, nullable=False)  # IMAGE, VIDEO, EXTERNAL_VIDEO
    alt = Column(String, nullable=True)
    position = Column(Integer, nullable=True)

    # URLs
    url = Column(String, nullable=True)
    preview_image_url = Column(String, nullable=True)

    # Video specific
    duration = Column(Integer, nullable=True)  # in seconds

    # Status
    status = Column(String, nullable=True)  # READY, PROCESSING, FAILED

    # Shopify timestamps
    shopify_created_at = Column(DateTime(timezone=True), nullable=True)
    shopify_updated_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<ShopifyProductMedia(id={self.id}, external_id='{self.external_id}', media_type='{self.media_type}')>"
