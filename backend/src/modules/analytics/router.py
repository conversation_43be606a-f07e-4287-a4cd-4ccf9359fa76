"""
Analytics API Router - Video performance tracking
"""

import logging
from datetime import datetime, timedelta
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.analytics.service import analytics_service
from modules.media_generation.schemas import AnalyticsResponse
from modules.analytics.event_service import analytics_event_service
from modules.analytics.event_schemas import (
    EventIngestionRequest, BatchEventIngestionRequest,
    EventIngestionResponse, BatchEventIngestionResponse,
    VideoAnalyticsRequest, VideoAnalyticsResponse,
    ConversionFunnelRequest, ConversionFunnelResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/product/{product_id}", response_model=AnalyticsResponse)
async def get_product_analytics(
    product_id: str,
    from_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    to_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    variant_id: Optional[int] = Query(None, description="Specific variant ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get analytics for a product's video performance.
    
    Returns metrics: views, plays, completion rate, avg watch time, CTR, conversions
    """
    try:
        # Parse dates
        start_date = None
        end_date = None
        
        if from_date:
            start_date = datetime.strptime(from_date, "%Y-%m-%d")
        else:
            start_date = datetime.now() - timedelta(days=30)
            
        if to_date:
            end_date = datetime.strptime(to_date, "%Y-%m-%d")
        else:
            end_date = datetime.now()
        
        # Validate product ownership
        from modules.stores.models import Store
        from modules.shopify.models import ShopifyIntegration, ShopifyProduct

        # Get user's stores
        user_stores = db.query(Store).filter(Store.owner_id == current_user.id).all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Get Shopify integrations for user's stores
        shopify_integrations = db.query(ShopifyIntegration).filter(
            ShopifyIntegration.store_id.in_(store_ids)
        ).all()

        if not shopify_integrations:
            raise HTTPException(
                status_code=404,
                detail="No Shopify integrations found for user's stores"
            )

        integration_ids = [integration.id for integration in shopify_integrations]

        # Check if product exists and belongs to user's integrations
        product = db.query(ShopifyProduct).filter(
            ShopifyProduct.external_id == product_id,
            ShopifyProduct.shopify_integration_id.in_(integration_ids)
        ).first()

        if not product:
            raise HTTPException(
                status_code=404,
                detail="Product not found or access denied"
            )

        # Get analytics data
        metrics = await analytics_service.get_product_metrics(
            db=db,
            product_id=product_id,
            variant_id=variant_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return AnalyticsResponse(
            product_id=product_id,
            variant_id=variant_id,
            metrics=metrics,
            period={
                "from": start_date.isoformat(),
                "to": end_date.isoformat()
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {e}")
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/events")
async def track_event(
    event_data: dict,
    db: AsyncSession = Depends(get_db),
):
    """
    Track analytics events (views, plays, conversions).
    
    Body: {variantId, eventType, userSessionId, viewport, device, ...}
    """
    try:
        await analytics_service.track_event(db, event_data)
        return {"status": "tracked"}
        
    except Exception as e:
        logger.error(f"Error tracking event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dashboard")
async def get_dashboard_metrics(
    days: int = Query(30, description="Number of days to look back"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get dashboard overview metrics."""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        metrics = await analytics_service.get_dashboard_metrics(
            db=db,
            user_id=current_user.id,
            start_date=start_date,
            end_date=end_date
        )
        
        return metrics

    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# New analytics event endpoints
@router.post("/events/ingest", response_model=EventIngestionResponse)
async def ingest_analytics_event(
    event_request: EventIngestionRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Ingest a single analytics event with deduplication."""
    try:
        # Get tenant ID from user (assuming user has tenant relationship)
        tenant_id = getattr(current_user, 'tenant_id', 1)  # Default to 1 for now

        # Get client IP
        ip_address = request.client.host if request.client else None

        response = await analytics_event_service.ingest_event(
            db, tenant_id, event_request, ip_address
        )
        return response

    except Exception as e:
        logger.error(f"Error ingesting event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/events/batch", response_model=BatchEventIngestionResponse)
async def ingest_batch_analytics_events(
    batch_request: BatchEventIngestionRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Ingest multiple analytics events in batch."""
    try:
        # Get client IP
        ip_address = request.client.host if request.client else None

        response = await analytics_event_service.ingest_batch_events(
            db, batch_request, ip_address
        )
        return response

    except Exception as e:
        logger.error(f"Error ingesting batch events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/video-analytics", response_model=VideoAnalyticsResponse)
async def get_detailed_video_analytics(
    analytics_request: VideoAnalyticsRequest = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get detailed video analytics for a product or variant."""
    try:
        # Get tenant ID from user
        tenant_id = getattr(current_user, 'tenant_id', 1)

        response = await analytics_event_service.get_video_analytics(
            db, tenant_id, analytics_request
        )
        return response

    except Exception as e:
        logger.error(f"Error getting video analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversion-funnel", response_model=ConversionFunnelResponse)
async def get_detailed_conversion_funnel(
    funnel_request: ConversionFunnelRequest = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get detailed conversion funnel analysis."""
    try:
        # Get tenant ID from user
        tenant_id = getattr(current_user, 'tenant_id', 1)

        response = await analytics_event_service.get_conversion_funnel(
            db, tenant_id, funnel_request
        )
        return response

    except Exception as e:
        logger.error(f"Error getting conversion funnel: {e}")
        raise HTTPException(status_code=500, detail=str(e))
