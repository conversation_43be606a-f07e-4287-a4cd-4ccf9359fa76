from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any
from datetime import datetime

from core.schemas.base_schemas import BaseSchema


class LoginRequest(BaseSchema):
    """Login request schema."""
    
    email: EmailStr
    password: str


class TokenResponse(BaseSchema):
    """Token response schema."""
    
    access_token: str
    token_type: str = "bearer"


class ForgotPasswordRequest(BaseSchema):
    """Forgot password request schema."""
    
    email: EmailStr


class ResetPasswordRequest(BaseSchema):
    """Reset password request schema."""

    token: str
    new_password: str


class UserCreate(BaseSchema):
    """User creation schema."""

    email: EmailStr
    password: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    avatar_url: Optional[str] = None
    is_verified: bool = False


class UserResponse(BaseSchema):
    """User response schema."""

    id: int
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_verified: bool
    role: str
    created_at: datetime
    last_login_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class OAuthUserInfo(BaseSchema):
    """OAuth user information schema."""

    provider_user_id: str
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    avatar_url: Optional[str] = None
    provider_data: Dict[str, Any] = {}


class OAuthAuthorizationRequest(BaseSchema):
    """OAuth authorization request schema."""

    provider: str
    redirect_uri: str
    shop_domain: Optional[str] = None  # For Shopify


class OAuthCallbackRequest(BaseSchema):
    """OAuth callback request schema."""

    provider: str
    code: str
    state: str
    redirect_uri: str
    shop_domain: Optional[str] = None  # For Shopify


class RegisterRequest(BaseSchema):
    """User registration request schema."""

    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v


class VerifyEmailRequest(BaseSchema):
    """Email verification request schema."""

    token: str


class ResendVerificationRequest(BaseSchema):
    """Resend verification email request schema."""

    email: EmailStr
