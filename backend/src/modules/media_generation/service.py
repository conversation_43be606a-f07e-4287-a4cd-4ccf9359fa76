"""
Video Generation Service - Core business logic for ProductVideo
"""

import logging
from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from core.config import get_settings
from modules.media_generation.models import (
    MediaJob, MediaVariant, Template, Voice, MediaJobStatus, MediaVariantStatus
)
from modules.media_generation.schemas import MediaGenerateRequest

from modules.media_generation.bullmq_service import bullmq_service, JobPriority
from modules.media_generation.storage_service import video_storage_service
from modules.shopify.sync_service import ShopifySyncService

logger = logging.getLogger(__name__)
settings = get_settings()


class MediaGenerationService(BaseService[MediaJob, dict, dict]):
    """Service for video generation operations."""

    def __init__(self):
        super().__init__(MediaJob)

    async def create_generation_jobs(
        self, 
        db: AsyncSession, 
        user_id: int, 
        request: MediaGenerateRequest
    ) -> List[MediaJob]:
        """Create media generation jobs for multiple products."""
        jobs = []
        
        for product_id in request.product_ids:
            # Create main job
            job = MediaJob(
                tenant_id=user_id,  # Using user_id as tenant_id for now
                product_id=product_id,
                status=MediaJobStatus.PENDING,
                media_type=request.media_type,
                provider=settings.VIDEO_PROVIDER,
                template_id=request.template_id,
                voice_id=request.voice_id,
                script=request.text_input, # Use text_input for voice generation
                custom_config={
                    "aspect_ratio": request.aspect_ratio,
                    "locale": request.locale
                }
            )
            
            db.add(job)
            await db.flush()  # Get the job ID
            
            # Create 4 variants per job (as per requirements)
            variants = [
                MediaVariant(
                    job_id=job.id,
                    variant_name="square",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    variant_name="vertical",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    variant_name="horizontal",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    variant_name="story",
                    status=MediaVariantStatus.GENERATING
                )
            ]
            
            for variant in variants:
                db.add(variant)
            
            jobs.append(job)
        
        await db.commit()
        return jobs

    async def get_job_with_variants(self, db: AsyncSession, job_id: int) -> Optional[MediaJob]:
        """Get job with its variants."""
        result = await db.execute(
            select(MediaJob).where(MediaJob.id == job_id)
        )
        return result.scalar_one_or_none()

    async def process_generation_job(self, db: AsyncSession, job_id: int):
        """Process a media generation job (background task)."""
        logger.info(f"Processing media generation job {job_id}")

        # Get the job to extract details
        job = await self.get(db, job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Queue the job for background processing using BullMQ
        bullmq_job_id = await bullmq_service.enqueue_media_generation(
            tenant_id=job.tenant_id,
            job_id=job_id,
            product_ids=[job.product_id] if job.product_id else [],
            media_type=job.media_type,
            template_id=job.template_id or "default",
            voice_id=job.voice_id or "default",
            text_input=job.script, # Pass script as text_input for voice generation
            priority=JobPriority.NORMAL
        )

        logger.info(f"Media generation job {job_id} queued for processing with BullMQ job ID {bullmq_job_id}")

    async def regenerate_variant(
        self, 
        job_id: int, 
        variant_id: int, 
        override_params: Optional[Dict[str, Any]] = None
    ):
        """Regenerate a specific media variant."""
        # TODO: Implement regeneration logic
        logger.info(f"Regenerating variant {variant_id} for job {job_id}")

    async def push_to_shopify(
        self, 
        shop_id: int, 
        product_id: str, 
        variant_id: int, 
        publish_options: Optional[Dict[str, Any]] = None
    ):
        """Push media variant to Shopify product media."""
        # TODO: Implement Shopify push logic
        logger.info(f"Pushing variant {variant_id} to Shopify product {product_id}")

    async def get_templates(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Get available media templates."""
        result = await db.execute(select(Template).where(Template.is_active == True))
        templates = result.scalars().all()
        
        return [{
            "id": str(template.id),
            "name": template.name,
            "description": template.description,
            "preview_url": template.preview_url,
            "category": template.category
        } for template in templates]

    async def get_voices(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Get available voices."""
        result = await db.execute(select(Voice).where(Voice.is_active == True))
        voices = result.scalars().all()
        
        return [{
            "id": str(voice.id),
            "name": voice.name,
            "gender": voice.gender,
            "accent": voice.accent,
            "language": voice.language,
            "sample_url": voice.sample_url
        } for voice in voices]


# Create service instance
media_generation_service = MediaGenerationService()
