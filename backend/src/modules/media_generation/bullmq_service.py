"""
BullMQ integration for ProductVideo platform.
Replaces custom queue service with mature BullMQ implementation.
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from enum import Enum

import redis.asyncio as redis
from bullmq import Queue, Worker, Job

from core.config import get_settings
from .models import MediaJobStatus

logger = logging.getLogger(__name__)
settings = get_settings()


class JobPriority(Enum):
    """Job priority levels."""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    URGENT = 15


class BullMQService:
    """
    BullMQ service for managing media generation jobs.
    Provides a clean interface over BullMQ for our specific use cases.
    """
    
    def __init__(self):
        self.redis_url = settings.REDIS_URL
        self.connection = None
        
        # Queue names
        self.media_queue_name = "media-generation"
        self.push_queue_name = "media-push"
        self.analytics_queue_name = "analytics-processing"
        
        # Initialize queues
        self.media_queue = None
        self.push_queue = None
        self.analytics_queue = None
        
        logger.info("Initialized BullMQ service")
    
    async def connect(self):
        """Connect to Redis and initialize queues."""
        if not self.connection:
            self.connection = redis.from_url(self.redis_url)
            
            # Initialize BullMQ queues
            self.media_queue = Queue(
                self.media_queue_name,
                connection=self.connection
            )
            
            self.push_queue = Queue(
                self.push_queue_name,
                connection=self.connection
            )
            
            self.analytics_queue = Queue(
                self.analytics_queue_name,
                connection=self.connection
            )
            
            logger.info("Connected to Redis and initialized BullMQ queues")
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self.connection:
            await self.connection.close()
            self.connection = None
            logger.info("Disconnected from Redis")
    
    async def enqueue_media_generation(
        self,
        tenant_id: int,
        job_id: int,
        product_ids: List[str],
        media_type: str,
        template_id: Optional[str] = None,
        voice_id: Optional[str] = None,
        text_input: Optional[str] = None,
        priority: JobPriority = JobPriority.NORMAL,
        delay_ms: int = 0,
        **kwargs
    ) -> str:
        """
        Enqueue a media generation job.
        
        Args:
            tenant_id: Tenant ID
            job_id: Database job ID
            product_ids: List of product IDs to generate media for
            media_type: Type of media to generate ('video', 'image', 'voice')
            template_id: Template to use (for video/image)
            voice_id: Voice to use (for video/voice)
            text_input: Text input for voice generation
            priority: Job priority
            delay_ms: Delay before processing (milliseconds)
            **kwargs: Additional job data
            
        Returns:
            BullMQ job ID
        """
        await self.connect()
        
        job_data = {
            "tenant_id": tenant_id,
            "job_id": job_id,
            "product_ids": product_ids,
            "media_type": media_type,
            "template_id": template_id,
            "voice_id": voice_id,
            "text_input": text_input,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        job_options = {
            "priority": priority.value,
            "attempts": 3,
            "backoff": {
                "type": "exponential",
                "delay": 2000,
            },
            "removeOnComplete": 100,  # Keep last 100 completed jobs
            "removeOnFail": 50,       # Keep last 50 failed jobs
        }
        
        if delay_ms > 0:
            job_options["delay"] = delay_ms
        
        job = await self.media_queue.add(
            "generate-media",
            job_data,
            job_options
        )
        
        logger.info(f"Enqueued {media_type} generation job {job.id} for tenant {tenant_id}")
        return job.id
    
    async def enqueue_media_push(
        self,
        tenant_id: int,
        variant_id: int,
        product_id: str,
        shop_domain: str,
        priority: JobPriority = JobPriority.HIGH,
        **kwargs
    ) -> str:
        """
        Enqueue a media push to Shopify job.
        
        Args:
            tenant_id: Tenant ID
            variant_id: Media variant ID
            product_id: Shopify product ID
            shop_domain: Shopify shop domain
            priority: Job priority
            **kwargs: Additional job data
            
        Returns:
            BullMQ job ID
        """
        await self.connect()
        
        job_data = {
            "tenant_id": tenant_id,
            "variant_id": variant_id,
            "product_id": product_id,
            "shop_domain": shop_domain,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        job_options = {
            "priority": priority.value,
            "attempts": 5,  # More retries for Shopify API calls
            "backoff": {
                "type": "exponential",
                "delay": 5000,
            },
            "removeOnComplete": 50,
            "removeOnFail": 25,
        }
        
        job = await self.push_queue.add(
            "push-to-shopify",
            job_data,
            job_options
        )
        
        logger.info(f"Enqueued media push job {job.id} for variant {variant_id}")
        return job.id
    
    async def enqueue_analytics_processing(
        self,
        tenant_id: int,
        event_data: Dict[str, Any],
        priority: JobPriority = JobPriority.LOW,
        **kwargs
    ) -> str:
        """
        Enqueue analytics event processing.
        
        Args:
            tenant_id: Tenant ID
            event_data: Analytics event data
            priority: Job priority
            **kwargs: Additional job data
            
        Returns:
            BullMQ job ID
        """
        await self.connect()
        
        job_data = {
            "tenant_id": tenant_id,
            "event_data": event_data,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        job_options = {
            "priority": priority.value,
            "attempts": 2,
            "backoff": {
                "type": "fixed",
                "delay": 1000,
            },
            "removeOnComplete": 200,
            "removeOnFail": 100,
        }
        
        job = await self.analytics_queue.add(
            "process-analytics",
            job_data,
            job_options
        )
        
        logger.info(f"Enqueued analytics processing job {job.id}")
        return job.id
    
    async def get_job_status(self, queue_name: str, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get job status and details.
        
        Args:
            queue_name: Name of the queue
            job_id: BullMQ job ID
            
        Returns:
            Job status information
        """
        await self.connect()
        
        # Get the appropriate queue
        queue = None
        if queue_name == self.media_queue_name:
            queue = self.media_queue
        elif queue_name == self.push_queue_name:
            queue = self.push_queue
        elif queue_name == self.analytics_queue_name:
            queue = self.analytics_queue
        
        if not queue:
            return None
        
        job = await queue.getJob(job_id)
        if not job:
            return None
        
        return {
            "id": job.id,
            "name": job.name,
            "data": job.data,
            "progress": job.progress,
            "returnvalue": job.returnvalue,
            "failedReason": job.failedReason,
            "processedOn": job.processedOn,
            "finishedOn": job.finishedOn,
            "timestamp": job.timestamp,
            "attemptsMade": job.attemptsMade,
            "delay": job.delay,
            "priority": job.opts.get("priority", 0),
        }
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues."""
        await self.connect()
        
        stats = {}
        
        for queue_name, queue in [
            (self.media_queue_name, self.media_queue),
            (self.push_queue_name, self.push_queue),
            (self.analytics_queue_name, self.analytics_queue),
        ]:
            queue_stats = await queue.getJobCounts()
            stats[queue_name] = {
                "waiting": queue_stats.get("waiting", 0),
                "active": queue_stats.get("active", 0),
                "completed": queue_stats.get("completed", 0),
                "failed": queue_stats.get("failed", 0),
                "delayed": queue_stats.get("delayed", 0),
                "paused": queue_stats.get("paused", 0),
            }
        
        return stats
    
    async def retry_failed_jobs(
        self, 
        queue_name: str, 
        limit: int = 100
    ) -> int:
        """
        Retry failed jobs in a queue.
        
        Args:
            queue_name: Name of the queue
            limit: Maximum number of jobs to retry
            
        Returns:
            Number of jobs retried
        """
        await self.connect()
        
        # Get the appropriate queue
        queue = None
        if queue_name == self.media_queue_name:
            queue = self.media_queue
        elif queue_name == self.push_queue_name:
            queue = self.push_queue
        elif queue_name == self.analytics_queue_name:
            queue = self.analytics_queue
        
        if not queue:
            return 0
        
        failed_jobs = await queue.getFailed(0, limit - 1)
        retried_count = 0
        
        for job in failed_jobs:
            try:
                await job.retry()
                retried_count += 1
                logger.info(f"Retried failed job {job.id}")
            except Exception as e:
                logger.error(f"Failed to retry job {job.id}: {e}")
        
        return retried_count
    
    async def clean_old_jobs(self, hours_old: int = 24) -> Dict[str, int]:
        """
        Clean old completed and failed jobs.
        
        Args:
            hours_old: Remove jobs older than this many hours
            
        Returns:
            Count of cleaned jobs per queue
        """
        await self.connect()
        
        grace_period = hours_old * 60 * 60 * 1000  # Convert to milliseconds
        cleaned_counts = {}
        
        for queue_name, queue in [
            (self.media_queue_name, self.media_queue),
            (self.push_queue_name, self.push_queue),
            (self.analytics_queue_name, self.analytics_queue),
        ]:
            try:
                # Clean completed jobs
                completed_cleaned = await queue.clean(grace_period, "completed")
                
                # Clean failed jobs
                failed_cleaned = await queue.clean(grace_period, "failed")
                
                total_cleaned = completed_cleaned + failed_cleaned
                cleaned_counts[queue_name] = total_cleaned
                
                logger.info(f"Cleaned {total_cleaned} old jobs from {queue_name}")
                
            except Exception as e:
                logger.error(f"Failed to clean jobs from {queue_name}: {e}")
                cleaned_counts[queue_name] = 0
        
        return cleaned_counts


# Create service instance
bullmq_service = BullMQService()
