import { Inter } from 'next/font/google';
import './globals.css';
import { Metadata, Viewport } from 'next';
import { Providers } from '@/components/providers/Providers';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: 'E-Commerce Management Platform',
  description:
    'Comprehensive e-commerce management platform with product management, order tracking, and analytics',
  keywords: ['e-commerce', 'product management', 'order tracking', 'analytics'],
  authors: [{ name: 'E-Commerce Platform Team' }],
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#ffffff" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${inter.variable} font-sans antialiased bg-gradient-to-br from-background via-background to-muted/20 text-foreground`}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
