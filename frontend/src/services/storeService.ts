import { api } from "./api";

export interface Store {
  id: number;
  name: string;
  platform: string;
  shop_domain?: string;
  shop_name?: string;
  is_active: boolean;
  last_sync: string | null;
}

export interface StoreCreate {
  name: string;
  admin_access_token: string;
  storefront_access_token?: string;
}

export interface ConnectionTest {
  success: boolean;
  message: string;
  store_info?: any;
}

export const storeService = {
  async getStores(): Promise<Store[]> {
    const response = await api.get("/api/stores/");
    return response.data;
  },

  async createStore(store: StoreCreate): Promise<Store> {
    const response = await api.post("/api/stores/", store);
    return response.data;
  },

  async testConnection(store: StoreCreate): Promise<ConnectionTest> {
    const response = await api.post("/api/stores/test-connection", store);
    return response.data;
  },

  async syncStore(storeId: number): Promise<any> {
    const response = await api.post(`/api/stores/${storeId}/sync`);
    return response.data;
  },

  async toggleStoreActivation(storeId: number): Promise<Store> {
    const response = await api.post(
      `/api/stores/${storeId}/toggle-activation`
    );
    return response.data;
  },
};
