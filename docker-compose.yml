version: "3.8"

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app_user -d ecommerce_db"]
      interval: 10s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis for job queue and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  # MinIO for S3-compatible storage
  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # MinIO Client for bucket setup
  minio-setup:
    image: minio/mc:latest
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      mc alias set myminio http://minio:9000 minioadmin minioadmin123;
      mc mb myminio/ecommerce-videos --ignore-existing;
      mc policy set public myminio/ecommerce-videos;
      echo 'MinIO setup complete';
      "
    restart: on-failure

  # ProductVideo Backend API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    command: /bin/bash -c "alembic upgrade head && python src/servers/api/main.py"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Video Generation Workers
  video-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
    env_file:
      - ./backend/.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
    command: python -m src.servers.worker.bullmq_worker
    restart: unless-stopped
    deploy:
      replicas: 2

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env
    volumes:
      - ./frontend:/app
      - frontend_data:/app/node_modules
    depends_on:
      - api
    command: npm run dev -- --port 3000
    restart: unless-stopped

  # Database Admin (Development)
  pgweb:
    image: sosedoff/pgweb
    ports:
      - "8081:8081"
    environment:
      DATABASE_URL: ****************************************/ecommerce_db?sslmode=disable
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    restart: unless-stopped

  # Nginx for reverse proxy and static files
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443" # For HTTPS, if configured
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      # Mount backend static files if needed by Nginx
      - ./backend/storage:/var/www/static # Assuming backend static files are in backend/storage
      # Mount .htpasswd if used for admin panel
      # - ./nginx/.htpasswd:/etc/nginx/.htpasswd # You'll need to create this file
    depends_on:
      - api
      - frontend
      - minio # If Nginx proxies to MinIO directly
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:
  frontend_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: productvideo_network
