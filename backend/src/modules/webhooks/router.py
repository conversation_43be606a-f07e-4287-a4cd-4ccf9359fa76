"""
Webhooks API Router - Handle Shopify and other platform webhooks
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Request, Header
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.webhooks.shopify_service import ShopifyWebhookService
from modules.stores.service import store_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/shopify/products_create")
async def shopify_product_create(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify product creation webhook."""
    return await _handle_shopify_webhook(
        request, "products/create", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/products_update")
async def shopify_product_update(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify product update webhook."""
    return await _handle_shopify_webhook(
        request, "products/update", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/products_delete")
async def shopify_product_delete(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify product deletion webhook."""
    return await _handle_shopify_webhook(
        request, "products/delete", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/orders_create")
async def shopify_order_create(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify order creation webhook."""
    return await _handle_shopify_webhook(
        request, "orders/create", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/orders_updated")
async def shopify_order_update(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify order update webhook."""
    return await _handle_shopify_webhook(
        request, "orders/updated", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/app_uninstalled")
async def shopify_app_uninstall(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify app uninstall webhook."""
    return await _handle_shopify_webhook(
        request, "app/uninstalled", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


@router.post("/shopify/shop_update")
async def shopify_shop_update(
    request: Request,
    x_shopify_hmac_sha256: str = Header(...),
    x_shopify_shop_domain: str = Header(...),
    db: AsyncSession = Depends(get_db),
):
    """Handle Shopify shop update webhook."""
    return await _handle_shopify_webhook(
        request, "shop/update", x_shopify_hmac_sha256, x_shopify_shop_domain, db
    )


async def _handle_shopify_webhook(
    request: Request,
    topic: str,
    signature: str,
    shop_domain: str,
    db: AsyncSession,
) -> Dict[str, Any]:
    """
    Common handler for Shopify webhooks.
    
    Args:
        request: FastAPI request object
        topic: Webhook topic
        signature: HMAC signature from Shopify
        shop_domain: Shop domain from header
        db: Database session
        
    Returns:
        Processing result
    """
    try:
        # Get raw body for signature verification
        body = await request.body()
        
        # Find the store by domain
        stores = await store_service.get_by_platform(db, "shopify")
        store = None
        for s in stores:
            if s.shop_domain == shop_domain:
                store = s
                break
        
        if not store:
            logger.error(f"Store not found for domain: {shop_domain}")
            raise HTTPException(status_code=404, detail="Store not found")
        
        # Initialize webhook service
        webhook_service = ShopifyWebhookService(store)
        
        # Verify signature
        if not webhook_service.verify_webhook_signature(body, signature):
            logger.error(f"Invalid webhook signature for {topic} from {shop_domain}")
            raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Parse JSON payload
        try:
            payload = await request.json()
        except Exception as e:
            logger.error(f"Failed to parse webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        # Extract webhook ID from headers if available
        webhook_id = request.headers.get("X-Shopify-Webhook-Id") or f"{topic}_{int(datetime.utcnow().timestamp())}"

        # Process the webhook with enhanced tracking
        result = await webhook_service.process_webhook(topic, payload, db, webhook_id)

        logger.info(f"Webhook {topic} processed successfully for {shop_domain}")
        return {"status": "success", "result": result, "webhook_id": webhook_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing webhook {topic}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health")
async def webhook_health():
    """Health check endpoint for webhook service."""
    return {"status": "healthy", "service": "webhooks"}


@router.get("/stats")
async def webhook_stats(db: AsyncSession = Depends(get_db)):
    """Get webhook processing statistics."""
    try:
        from modules.webhooks.shopify_service import WebhookEvent, WebhookProcessingStatus
        from sqlalchemy import func, and_
        from datetime import datetime, timedelta

        # Calculate time ranges
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)

        # Get total webhook events
        total_result = await db.execute(select(func.count(WebhookEvent.id)))
        total_processed = total_result.scalar()

        # Get successful webhooks
        success_result = await db.execute(
            select(func.count(WebhookEvent.id)).where(
                WebhookEvent.status == WebhookProcessingStatus.COMPLETED.value
            )
        )
        successful = success_result.scalar()

        # Get failed webhooks
        failed_result = await db.execute(
            select(func.count(WebhookEvent.id)).where(
                WebhookEvent.status == WebhookProcessingStatus.FAILED.value
            )
        )
        failed = failed_result.scalar()

        # Get webhooks in last 24 hours
        last_24h_result = await db.execute(
            select(func.count(WebhookEvent.id)).where(
                WebhookEvent.created_at >= last_24h
            )
        )
        last_24h_count = last_24h_result.scalar()

        # Get webhooks in last 7 days
        last_7d_result = await db.execute(
            select(func.count(WebhookEvent.id)).where(
                WebhookEvent.created_at >= last_7d
            )
        )
        last_7d_count = last_7d_result.scalar()

        # Calculate success rate
        success_rate = (successful / max(total_processed, 1)) * 100

        # Get topic breakdown
        topic_stats = await db.execute(
            select(
                WebhookEvent.topic,
                func.count(WebhookEvent.id)
            ).group_by(WebhookEvent.topic)
        )

        # Get status breakdown
        status_stats = await db.execute(
            select(
                WebhookEvent.status,
                func.count(WebhookEvent.id)
            ).group_by(WebhookEvent.status)
        )

        return {
            "total_processed": total_processed,
            "successful": successful,
            "failed": failed,
            "success_rate": round(success_rate, 2),
            "last_24h": last_24h_count,
            "last_7d": last_7d_count,
            "topic_breakdown": dict(topic_stats),
            "status_breakdown": dict(status_stats),
            "timestamp": now.isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting webhook stats: {e}")
        return {
            "total_processed": 0,
            "success_rate": 0.0,
            "last_24h": 0,
            "error": str(e)
        }
