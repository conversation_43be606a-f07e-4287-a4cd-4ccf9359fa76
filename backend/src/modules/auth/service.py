from datetime import datetime, timedelta
from typing import Optional

from jose import JW<PERSON><PERSON>r, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.auth.models import User, OAuthAccount


class AuthService:
    """Service for authentication operations."""

    def __init__(self):
        self.settings = get_settings()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.settings.SECRET_KEY, algorithm=self.settings.ALGORITHM)
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[dict]:
        """Verify JWT token and return payload."""
        try:
            payload = jwt.decode(token, self.settings.SECRET_KEY, algorithms=[self.settings.ALGORITHM])
            return payload
        except JWTError:
            return None

    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)

    async def get_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """Get a user by email address."""
        from sqlalchemy import select
        result = await db.execute(
            select(User).where(User.email == email)
        )
        return result.scalars().first()

    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """Get a user by ID."""
        from sqlalchemy import select
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalars().first()

    async def create_user(self, db: AsyncSession, user_data) -> User:
        """Create a new user."""
        user = User(
            email=user_data.email,
            password_hash=self.hash_password(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            username=user_data.username,
            is_active=True,
            is_verified=False
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user

    def create_refresh_token(self, data: dict) -> str:
        """Create a refresh JWT token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.settings.SECRET_KEY, algorithm=self.settings.ALGORITHM)
        return encoded_jwt

    def verify_token(self, token: str, token_type: str = "access") -> Optional[dict]:
        """Verify JWT token and return payload."""
        try:
            payload = jwt.decode(token, self.settings.SECRET_KEY, algorithms=[self.settings.ALGORITHM])
            if payload.get("type") == token_type or token_type == "access":
                return payload
            return None
        except JWTError:
            return None

    async def update_last_login(self, db: AsyncSession, user_id: int):
        """Update user's last login timestamp."""
        from sqlalchemy import select, update
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(last_login_at=datetime.utcnow())
        )
        await db.commit()

    async def authenticate_user(self, db: AsyncSession, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        user = await self.get_by_email(db, email)
        if not user:
            return None
        if not self.verify_password(password, user.password_hash):
            return None
        return user

    async def get_current_user(self, db: AsyncSession, token: str) -> Optional[User]:
        """Get current user from JWT token."""
        payload = self.verify_token(token)
        if payload is None:
            return None

        email: str = payload.get("sub")
        if email is None:
            return None

        user = await self.get_by_email(db, email)
        return user

    async def disconnect_oauth_provider(self, db: AsyncSession, user_id: int, provider: str) -> bool:
        """Disconnect OAuth provider from user account."""
        from sqlalchemy import select, delete

        # Check if the OAuth account exists
        stmt = select(OAuthAccount).where(
            OAuthAccount.user_id == user_id,
            OAuthAccount.provider == provider
        )
        result = await db.execute(stmt)
        oauth_account = result.scalar_one_or_none()

        if not oauth_account:
            return False

        # Delete the OAuth account
        stmt = delete(OAuthAccount).where(
            OAuthAccount.user_id == user_id,
            OAuthAccount.provider == provider
        )
        await db.execute(stmt)
        await db.commit()

        return True

    async def get_user_oauth_providers(self, db: AsyncSession, user_id: int):
        """Get list of connected OAuth providers for a user."""
        from sqlalchemy import select

        stmt = select(OAuthAccount).where(OAuthAccount.user_id == user_id)
        result = await db.execute(stmt)
        oauth_accounts = result.scalars().all()

        return oauth_accounts


# Create service instance
auth_service = AuthService()
