'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, FolderSyncIcon, Clock, CheckCircle } from 'lucide-react';

const SyncSettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Sync Settings</h1>
        <p className="text-muted-foreground">Configure synchronization settings for your stores</p>
      </div>

      {/* Sync Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FolderSyncIcon className="h-5 w-5" />
            <span>Synchronization Status</span>
          </CardTitle>
          <CardDescription>Current status of data synchronization across platforms</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-green-50 border border-green-200">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <p className="font-semibold text-green-900">Products</p>
                <p className="text-sm text-green-700">Last synced 5 minutes ago</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-green-50 border border-green-200">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <p className="font-semibold text-green-900">Orders</p>
                <p className="text-sm text-green-700">Last synced 2 minutes ago</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-blue-50 border border-blue-200">
              <Clock className="h-8 w-8 text-blue-600" />
              <div>
                <p className="font-semibold text-blue-900">Inventory</p>
                <p className="text-sm text-blue-700">Syncing now...</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sync Frequency */}
      <Card>
        <CardHeader>
          <CardTitle>Sync Frequency</CardTitle>
          <CardDescription>Configure how often data should be synchronized</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium">Product Sync</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>Every 15 minutes</option>
                <option>Every 30 minutes</option>
                <option>Every hour</option>
                <option>Every 6 hours</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium">Order Sync</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>Every 5 minutes</option>
                <option>Every 15 minutes</option>
                <option>Every 30 minutes</option>
                <option>Every hour</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium">Inventory Sync</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>Real-time</option>
                <option>Every 5 minutes</option>
                <option>Every 15 minutes</option>
                <option>Every 30 minutes</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium">Customer Sync</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>Every hour</option>
                <option>Every 6 hours</option>
                <option>Daily</option>
                <option>Manual only</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button variant="outline">Reset to Default</Button>
            <Button>Save Settings</Button>
          </div>
        </CardContent>
      </Card>

      {/* Manual Sync */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Synchronization</CardTitle>
          <CardDescription>Trigger immediate synchronization for specific data types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button variant="outline" className="h-20 flex-col">
              <FolderSyncIcon className="h-6 w-6 mb-2" />
              Sync Products
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <FolderSyncIcon className="h-6 w-6 mb-2" />
              Sync Orders
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <FolderSyncIcon className="h-6 w-6 mb-2" />
              Sync Inventory
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <FolderSyncIcon className="h-6 w-6 mb-2" />
              Sync All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SyncSettings;
