import json
import logging
import socket
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

import core.db.models  # Import all models to register them
from core.config import get_settings
from core.db.database import AsyncSessionLocal as SessionLocal
from core.middleware.request_context_middleware import RequestContextMiddleware
from core.middleware.request_logging_middleware import request_logging_middleware
from core.utils.logging import setup_logging

# Import module routers
from modules.auth.router import router as auth_router
from modules.stores.router import router as stores_router
from modules.shopify.router import router as shopify_router
from modules.media_generation.router import router as media_generation_router
from modules.analytics.router import router as analytics_router
from modules.webhooks.router import router as webhooks_router
from modules.storage.router import router as storage_router
from modules.templates.router import router as templates_router
from modules.voices.router import router as voices_router
from modules.billing.router import router as billing_router
from modules.admin.router import router as admin_router
# from modules.billing.router import router as billing_router

# Setup logging as early as possible
setup_logging()

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    
    # Startup events
    host = "localhost"
    port = settings.PORT

    try:
        network_ip = socket.gethostbyname(socket.gethostname())
    except socket.gaierror:
        network_ip = "<network_ip>"

    logger.info("\n" + "=" * 40)
    logger.info("🚀 E-commerce Integration Backend Started")
    logger.info(f"- Local:   http://{host}:{port}")
    logger.info(f"- Network: http://{network_ip}:{port}")
    logger.info(f"- Environment: {settings.ENVIRONMENT}")
    logger.info("=" * 40 + "\n")

    yield

    # Shutdown events
    logger.info("🛑 E-commerce Integration Backend Shutting Down")


app = FastAPI(
    title="E-commerce Integration API",
    description="API for integrating with Shopify and WooCommerce stores",
    version="1.0.0",
    lifespan=lifespan
)

# Add the request context middleware
app.add_middleware(RequestContextMiddleware)

# Add CORS middleware
settings = get_settings()
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request Logging Middleware
app.middleware("http")(request_logging_middleware)

# Include routers
app.include_router(auth_router, prefix="/api/auth", tags=["authentication"])
app.include_router(stores_router, prefix="/api/stores", tags=["stores"])
app.include_router(shopify_router, prefix="/api/shopify", tags=["shopify"])
app.include_router(media_generation_router, prefix="/api/media", tags=["media-generation"])
app.include_router(analytics_router, prefix="/api/analytics", tags=["analytics"])
app.include_router(webhooks_router, prefix="/api/webhooks", tags=["webhooks"])
app.include_router(storage_router, prefix="/api/storage", tags=["storage"])
app.include_router(templates_router, prefix="/api/templates", tags=["templates"])
app.include_router(voices_router, prefix="/api/voices", tags=["voices"])
app.include_router(admin_router, prefix="/api/admin", tags=["admin"])
app.include_router(billing_router, prefix="/api/billing", tags=["billing"])


# Log all registered routes
registered_routes = []
for route in app.routes:
    if hasattr(route, "path") and hasattr(route, "name"):
        registered_routes.append({"path": route.path, "name": route.name})
registered_routes_sorted = sorted(registered_routes, key=lambda x: x["path"])
print("Registered API Routes:\n" + json.dumps(registered_routes_sorted, indent=2))


@app.get("/")
async def read_root():
    """Root endpoint."""
    return {
        "message": "E-commerce Integration API", 
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    from datetime import datetime
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "service": "E-commerce Integration API"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Global exception handler caught: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "src.servers.api.main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development"
    )
