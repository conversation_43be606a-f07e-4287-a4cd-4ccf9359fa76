'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Store, Plus, Settings, BarChart3 } from 'lucide-react';

const Stores: React.FC = () => {
  const stores = [
    { id: 1, name: 'Main Store', platform: 'Shopify', status: 'Connected', products: 234, orders: 1567 },
    { id: 2, name: 'Amazon Store', platform: 'Amazon', status: 'Connected', products: 156, orders: 892 },
    { id: 3, name: 'eBay Store', platform: 'eBay', status: 'Disconnected', products: 89, orders: 234 },
    { id: 4, name: 'Etsy Shop', platform: 'Etsy', status: 'Connected', products: 67, orders: 345 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Stores</h1>
          <p className="text-muted-foreground">Manage your connected store platforms</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Connect Store
        </Button>
      </div>

      {/* Stores Grid */}
      <div className="grid gap-4 md:grid-cols-2">
        {stores.map((store) => (
          <Card key={store.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Store className="h-8 w-8 text-muted-foreground" />
                  <div>
                    <CardTitle>{store.name}</CardTitle>
                    <CardDescription>{store.platform}</CardDescription>
                  </div>
                </div>
                <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                  store.status === 'Connected' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {store.status}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{store.products}</p>
                  <p className="text-sm text-muted-foreground">Products</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">{store.orders}</p>
                  <p className="text-sm text-muted-foreground">Orders</p>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Analytics
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Stores;
