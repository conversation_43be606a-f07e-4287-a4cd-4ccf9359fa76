from typing import Any, AsyncGenerator, <PERSON><PERSON>

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from core.config import get_settings


def get_db_components(database_url: str, echo: bool = False) -> Tuple[Any, Any]:
    """
    Returns a SQLAlchemy async engine and an async sessionmaker.
    """
    engine = create_async_engine(database_url, echo=echo)
    AsyncSessionLocal = async_sessionmaker(
        expire_on_commit=False,
        autoflush=False,
        autocommit=False,
        bind=engine,
        class_=AsyncSession,
    )
    return engine, AsyncSessionLocal


def get_sync_db_components(database_url: str, echo: bool = False) -> Tuple[Any, Any]:
    """
    Returns a SQLAlchemy sync engine and sessionmaker for migrations and sync operations.
    """
    # Convert async URL to sync URL for migrations
    sync_url = database_url.replace("+asyncpg", "").replace("+aiosqlite", "")
    
    settings = get_settings()
    if settings.TESTING:
        engine = create_engine(
            "sqlite:///:memory:",
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )
    else:
        engine = create_engine(sync_url, echo=echo)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return engine, SessionLocal


settings = get_settings()

# Create the SQLAlchemy async engine and session local for the application
engine, AsyncSessionLocal = get_db_components(settings.DATABASE_URL, echo=False)

# Create sync engine for migrations
sync_engine, SyncSessionLocal = get_sync_db_components(settings.DATABASE_URL, echo=False)

# Base class for declarative models
Base = declarative_base()


# Dependency to get an async database session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    db = AsyncSessionLocal()
    try:
        yield db
    finally:
        await db.close()


# Dependency to get a sync database session (for migrations, etc.)
def get_sync_db():
    db = SyncSessionLocal()
    try:
        yield db
    finally:
        db.close()


# Function to get the session factory for services that need it
def get_db_session_factory():
    """Return the session factory for creating database sessions."""
    return AsyncSessionLocal


def get_sync_db_session_factory():
    """Return the sync session factory for creating database sessions."""
    return SyncSessionLocal
