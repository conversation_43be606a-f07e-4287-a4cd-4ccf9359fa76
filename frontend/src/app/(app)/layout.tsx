'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AppShell } from '@/components/layout';
import { Loader2 } from 'lucide-react';
import { LayoutProvider } from '@/contexts/LayoutContext';

// Simple protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute>
      <LayoutProvider>
        <AppShell>{children}</AppShell>
      </LayoutProvider>
    </ProtectedRoute>
  );
}
