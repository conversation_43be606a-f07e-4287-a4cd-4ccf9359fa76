from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from modules.stores.models import Store
from modules.stores.schemas import StoreCreate, StoreUpdate


class StoreService(BaseService[Store, StoreCreate, StoreUpdate]):
    """Service for store operations."""

    def __init__(self):
        super().__init__(Store)

    async def get_by_owner(self, db: AsyncSession, owner_id: int) -> List[Store]:
        """Get all stores owned by a user."""
        result = await db.execute(select(Store).where(Store.owner_id == owner_id))
        return result.scalars().all()

    async def get_by_platform(self, db: AsyncSession, platform: str) -> List[Store]:
        """Get all stores by platform."""
        result = await db.execute(select(Store).where(Store.platform == platform))
        return result.scalars().all()

    async def get_active_stores(self, db: AsyncSession) -> List[Store]:
        """Get all active stores."""
        result = await db.execute(select(Store).where(Store.is_active == True))
        return result.scalars().all()

    async def create_store(self, db: AsyncSession, store_create: StoreCreate, owner_id: int) -> Store:
        """Create a new store."""
        store_data = store_create.model_dump()
        store_data["owner_id"] = owner_id
        
        db_store = Store(**store_data)
        db.add(db_store)
        await db.commit()
        await db.refresh(db_store)
        return db_store

    async def test_connection(self, store: Store) -> dict:
        """Test connection to the store's platform."""
        # This would implement platform-specific connection testing
        # For now, return a mock response
        return {
            "success": True,
            "message": "Connection test not implemented yet",
            "store_info": {
                "platform": store.platform,
                "shop_domain": store.shop_domain,
                "shop_name": store.shop_name
            }
        }


# Create service instance
store_service = StoreService()
