"""
Video Generation API Router - Complete ProductVideo implementation
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.config import get_settings
from core.db.database import get_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.media_generation.bullmq_service import bullmq_service as job_queue_service, JobPriority
from modules.media_generation.models import (
    MediaJob, MediaVariant, Template, Voice, MediaJobStatus, MediaVariantStatus,
    VideoProvider, ImageProvider, VoiceProvider, PushStatus
)
from modules.media_generation.schemas import (
    MediaGenerateRequest, MediaGenerateResponse, MediaJobStatusResponse,
    MediaRegenerateRequest, MediaPushRequest, MediaPushResponse, MediaJobListResponse,
    MediaVariantListResponse, PushStatusResponse, MediaGenerationRequest,
    MediaVariantInfo, MediaJobInfo, MediaLimitsResponse
)
from modules.media_generation.service import media_generation_service
from modules.shopify.media_service import ShopifyMediaService
from modules.shopify.models import ShopifyIntegration
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_videos(
    request: MediaGenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate 4 video variants for selected products.
    
    Body: {shopId, productIds[], templateId?, voiceId?, aspectRatio?, locale?}
    Returns: job IDs per product-variant
    """
    try:
        # Validate shop ownership
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if hasattr(request, 'shop_id') and request.shop_id:
            if request.shop_id not in store_ids:
                raise HTTPException(
                    status_code=403,
                    detail="Access denied: shop does not belong to user"
                )

        # Create video generation jobs
        jobs = await media_generation_service.create_generation_jobs(
            db=db,
            user_id=current_user.id,
            request=request
        )
        
        # Queue background generation tasks using BullMQ
        for job in jobs:
            background_tasks.add_task(
                media_generation_service.process_generation_job,
                db,
                job.id
            )
        
        return MediaGenerateResponse(
            jobs=[{
                "product_id": job.product_id,
                "job_id": job.id,
                "status": job.status.value
            } for job in jobs]
        )
        
    except Exception as e:
        logger.error(f"Error generating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and variant IDs.
    """
    try:
        job = await media_generation_service.get_job_with_variants(db, job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Add ownership validation
        # Check if job belongs to user's tenant
        if hasattr(job, 'tenant_id') and job.tenant_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        return MediaJobStatusResponse(
            job_id=job.id,
            status=job.status.value,
            progress=job.progress_percentage,
            variants=[{
                "variant_id": variant.id,
                "variant_name": variant.variant_name,
                "status": variant.status.value,
                "video_url": variant.video_url,
                "image_url": variant.image_url,
                "voice_url": variant.voice_url,
                "thumbnail_url": variant.thumbnail_url,
                "duration": variant.duration_seconds
            } for variant in job.variants] if hasattr(job, 'variants') else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/regenerate")
async def regenerate_variant(
    request: MediaRegenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Regenerate a specific media variant.
    """
    try:
        # Get the original job and variant
        job = await media_generation_service.get_job_with_variants(db, request.job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Find the specific variant
        variant = None
        for v in job.variants:
            if str(v.id) == request.variant_id:
                variant = v
                break

        if not variant:
            raise HTTPException(status_code=404, detail="Variant not found")

        # Check ownership
        if hasattr(job, 'tenant_id') and job.tenant_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied: job does not belong to user")

        # Reset variant status
        variant.status = MediaVariantStatus.GENERATING
        variant.error_message = None
        variant.updated_at = datetime.now()

        # Update job status if needed
        if job.status == MediaJobStatus.COMPLETED:
            job.status = MediaJobStatus.PROCESSING

        await db.commit()

        # Re-queue the job with regeneration parameters
        background_tasks.add_task(
            media_generation_service.process_generation_job,
            db,
            job.id
        )

        logger.info(f"Regenerating variant {request.variant_id} for job {request.job_id}")
        return {
            "message": "Regeneration queued",
            "job_id": request.job_id,
            "variant_id": request.variant_id,
            "status": "queued"
        }
    except Exception as e:
        logger.error(f"Error regenerating variant: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_shopify(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push selected video variant to Shopify product media.

    Body: {shopId, productId, variantId, publishTargets, publishOptions}
    """
    try:
        # Validate shop ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(
                status_code=403,
                detail="Access denied: shop does not belong to user"
            )

        # Queue push task
        background_tasks.add_task(
            media_generation_service.push_to_shopify,
            request.shop_id,
            request.product_id,
            request.variant_id,
            request.publish_options
        )

        return MediaPushResponse(
            push_id=f"push_{request.variant_id}_{request.product_id}",
            status="queued",
            message="Push to Shopify queued"
        )

    except Exception as e:
        logger.error(f"Error pushing to Shopify: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates")
async def get_templates(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get available video templates."""
    try:
        templates = await media_generation_service.get_templates(db)
        return {"templates": templates}
    except Exception as e:
        logger.error(f"Error getting templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/voices")
async def get_voices(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get available voices."""
    try:
        voices = await media_generation_service.get_voices(db)
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Error getting voices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Build query
        query = select(MediaJob).filter(MediaJob.tenant_id == tenant.id)

        # Apply filters
        if status_filter:
            try:
                status_enum = MediaJobStatus(status_filter)
                query = query.filter(MediaJob.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )

        if product_id:
            query = query.filter(MediaJob.shopify_product_id == product_id)

        # Get total count
        total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

        # Apply pagination
        offset = (page - 1) * per_page
        jobs = (await db.execute(query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

        # Convert to response models
        job_responses = []
        for job in jobs:
            variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
            variant_responses = [MediaVariantInfo.from_orm(v) for v in variants]

            job_response = MediaJobInfo.from_orm(job)
            job_response.variants = variant_responses
            job_responses.append(job_response)

        return MediaJobListResponse(
            jobs=job_responses,
            total=total,
            page=page,
            per_page=per_page
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.get("/variants", response_model=MediaVariantListResponse)
async def list_variants(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    job_id: Optional[str] = Query(None),
    favorites_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media variants for the current user.

    Supports filtering by status, job ID, and favorites, with pagination.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Build query
        query = select(MediaVariant).filter(MediaVariant.tenant_id == tenant.id)

        # Apply filters
        if status_filter:
            try:
                status_enum = MediaVariantStatus(status_filter)
                query = query.filter(MediaVariant.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )

        if job_id:
            job = await db.execute(select(MediaJob).filter(MediaJob.external_job_id == job_id, MediaJob.tenant_id == tenant.id)).scalar_one_or_none()
            if job:
                query = query.filter(MediaVariant.job_id == job.id)
            else:
                # Job not found, return empty results
                return MediaVariantListResponse(variants=[], total=0, page=page, per_page=per_page)

        if favorites_only:
            query = query.filter(MediaVariant.is_favorite == True)

        # Get total count
        total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

        # Apply pagination
        offset = (page - 1) * per_page
        variants = (await db.execute(query.order_by(MediaVariant.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

        # Convert to response models
        variant_responses = [MediaVariantInfo.from_orm(v) for v in variants]

        return MediaVariantListResponse(
            variants=variant_responses,
            total=total,
            page=page,
            per_page=per_page
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list variants: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list variants: {str(e)}"
        )


@router.patch("/variants/{variant_id}/favorite")
async def toggle_variant_favorite(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Toggle the favorite status of a media variant.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(MediaVariant.external_variant_id == variant_id, MediaVariant.tenant_id == tenant.id)).scalar_one_or_none()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant not found"
            )

        # Toggle favorite status
        variant.is_favorite = not variant.is_favorite
        await db.commit()

        return {
            "success": True,
            "variant_id": variant_id,
            "is_favorite": variant.is_favorite,
            "message": f"Variant {'added to' if variant.is_favorite else 'removed from'} favorites"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to toggle favorite for variant {variant_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle favorite: {str(e)}"
        )


@router.patch("/variants/{variant_id}/rating")
async def rate_variant(
    variant_id: str,
    rating: int = Query(..., ge=1, le=5),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Rate a media variant (1-5 stars).
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(MediaVariant.external_variant_id == variant_id, MediaVariant.tenant_id == tenant.id)).scalar_one_or_none()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant not found"
            )

        # Update rating
        variant.user_rating = rating
        await db.commit()

        return {
            "success": True,
            "variant_id": variant_id,
            "rating": rating,
            "message": f"Variant rated {rating} stars"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to rate variant {variant_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to rate variant: {str(e)}"
        )


@router.delete("/jobs/{job_id}")
async def cancel_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cancel a media generation job.

    Cancels the job with the AI provider and updates the local status.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the job in database
        job = await db.execute(select(MediaJob).filter(MediaJob.external_job_id == job_id, MediaJob.tenant_id == tenant.id)).scalar_one_or_none()

        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )

        # Cancel the job in queue if it's still pending
        queue_cancelled = await job_queue_service.cancel_job(job_id)

        # Update job status in database
        if job.status in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
            job.status = MediaJobStatus.CANCELLED
            job.completed_at = datetime.now()

            # Mark variants as failed
            variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
            for variant in variants:
                variant.status = MediaVariantStatus.FAILED
                variant.updated_at = datetime.now()

            await db.commit()

        return {
            "success": True,
            "job_id": job_id,
            "message": "Job cancelled successfully",
            "queue_cancelled": queue_cancelled
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to cancel job {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel job: {str(e)}"
        )


@router.get("/processing/stats")
async def get_processing_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get media processing statistics.

    Returns information about current processing status and provider info.
    """
    try:
        # Add admin role check
        # For now, check if user email contains 'admin' or is in a hardcoded list
        admin_emails = getattr(settings, 'ADMIN_EMAILS', ['<EMAIL>', '<EMAIL>'])
        if current_user.email not in admin_emails and 'admin' not in current_user.email.lower():
            raise HTTPException(
                status_code=403,
                detail="Admin access required"
            )

        queue_stats = await job_queue_service.get_queue_stats()

        # Get provider info from settings
        provider_name = settings.VIDEO_PROVIDER # Keep VIDEO_PROVIDER for now, will update in core/config.py
        provider_info = {
            "provider": provider_name,
            "supports_templates": True,
            "supports_voices": True,
            "max_variants": 4,
            "supported_aspect_ratios": ["16:9", "9:16", "1:1"],
            "supported_locales": ["en", "es", "fr", "de", "it"]
        }

        return {
            "queue_stats": queue_stats,
            "provider_info": provider_info,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.exception(f"Failed to get processing stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get processing stats: {str(e)}"
        )


@router.get("/limits/{product_id}", response_model=MediaLimitsResponse)
async def get_media_limits(
    product_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get media limits for a Shopify product.
    
    Returns current video count and remaining capacity.
    """
    try:
        # Find user's store
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.platform == 'shopify',
            Store.is_active == True
        )).scalar_one_or_none()
        
        if not store or not store.admin_access_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or not connected"
            )
        
        # Get media limits
        media_service = ShopifyMediaService(store)
        limits = await media_service.get_media_limits(product_id)
        
        return MediaLimitsResponse(**limits)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get media limits: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get media limits: {str(e)}"
        )


@router.get("/status/{variant_id}", response_model=PushStatusResponse)
async def get_push_status(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get push status for a media variant.
    
    Returns current push status and Shopify media information.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()
        
        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )
        
        return PushStatusResponse(
            variant_id=variant_id,
            status=variant.push_status,
            shopify_media_id=variant.shopify_media_id,
            pushed_at=variant.pushed_to_shopify_at.isoformat() if variant.pushed_to_shopify_at else None,
            error_message=variant.push_error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get push status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get push status: {str(e)}"
        )


@router.post("/retry/{variant_id}")
async def retry_push(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retry pushing a failed media variant to Shopify.
    
    Requeues the push job for processing.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()
        
        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )
        
        if variant.push_status != PushStatus.FAILED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only retry failed pushes"
            )
        
        # Get the job and store
        job = await db.execute(select(MediaJob).filter(MediaJob.id == variant.job_id)).scalar_one_or_none()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Associated job not found"
            )
        
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.platform == 'shopify',
            Store.is_active == True
        )).scalar_one_or_none()
        
        if not store or not store.admin_access_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or not connected"
            )
        
        # Reset status and queue retry
        variant.push_status = PushStatus.PUSHING
        variant.push_error_message = None
        
        job_id = await job_queue_service.enqueue_job(
            job_type="media_push",
            payload={
                "store_id": store.id,
                "variant_id": variant.external_variant_id,
                "product_id": job.shopify_product_id,
                "video_url": variant.video_url,
                "alt_text": variant.alt_text,
                "position": None,
                "replace_existing": False
            },
            priority=JobPriority.HIGH,
            max_attempts=3,
            timeout_seconds=300,
            metadata={
                "tenant_id": tenant.id,
                "variant_id": variant.external_variant_id,
                "product_id": job.shopify_product_id,
                "retry": True
            }
        )
        
        await db.commit()
        
        return {
            "success": True,
            "message": "Push retry queued successfully",
            "job_id": job_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to retry push: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retry push: {str(e)}"
        )


@router.delete("/remove/{variant_id}")
async def remove_from_shopify(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Remove a media from Shopify product media.
    
    Deletes the media from Shopify and updates the variant status.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()
        
        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )
        
        if variant.push_status != PushStatus.COMPLETED or not variant.shopify_media_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Media is not currently in Shopify"
            )
        
        # Get the job and store
        job = await db.execute(select(MediaJob).filter(MediaJob.id == variant.job_id)).scalar_one_or_none()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Associated job not found"
            )
        
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.platform == 'shopify',
            Store.is_active == True
        )).scalar_one_or_none()
        
        if not store or not store.admin_access_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or not connected"
            )
        
        # Delete from Shopify
        media_service = ShopifyMediaService(store)
        product_gid = f"gid://shopify/Product/{job.shopify_product_id}"
        
        success = await media_service.delete_product_media(
            variant.shopify_media_id # Assuming this takes media_id directly
        )
        
        if success:
            # Update variant status
            variant.push_status = PushStatus.PENDING
            variant.shopify_media_id = None
            variant.pushed_to_shopify_at = None
            await db.commit()
            
            return {
                "success": True,
                "message": "Media removed from Shopify successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to remove media from Shopify"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to remove media from Shopify: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove media: {str(e)}"
        )
