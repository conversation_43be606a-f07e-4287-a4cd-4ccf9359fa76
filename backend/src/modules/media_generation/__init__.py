"""
Media Generation Module
Provides AI-powered media generation services for ProductVideo platform.
"""

# Main services
from modules.media_generation.image_service import ai_image_service
from modules.media_generation.video_service import ai_video_service
from modules.media_generation.voice_service import ai_voice_service

# Core components
from modules.media_generation.service import media_generation_service
from modules.media_generation.bullmq_service import bullmq_service
from modules.media_generation.storage_service import video_storage_service
from modules.media_generation.template_service import template_service
from modules.media_generation.transcoding_service import video_transcoding_service

# Models and schemas
from modules.media_generation import models
from modules.media_generation import schemas

__all__ = [
    # Services
    "ai_image_service",
    "ai_video_service",
    "ai_voice_service",
    "media_generation_service",
    "bullmq_service",
    "video_storage_service",
    "template_service",
    "video_transcoding_service",

    # Modules
    "models",
    "schemas",
]
